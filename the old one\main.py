import streamlit as st
import pandas as pd
import json
import io
import sys
import os
from collections import defaultdict, Counter
import re 
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from openai import OpenAI
import contextlib
import hashlib # Added for hashing input JSON


# Import configurations and core logic from other modules
import config
import utils
import preprocess
import rich_text_checker
import translation_validator
import translation_scorer
import literal_translator
import translation_decomposer

# --- Checkpoint Configuration ---
CHECKPOINT_DIR = ".jiaoyan_checkpoints"

def ensure_checkpoint_dir_exists():
    """确保检查点目录存在"""
    if not os.path.exists(CHECKPOINT_DIR):
        try:
            os.makedirs(CHECKPOINT_DIR)
        except OSError as e:
            st.error(f"无法创建检查点目录 '{CHECKPOINT_DIR}': {e}")

def get_input_hash(json_str):
    """计算输入JSON字符串的SHA256哈希值"""
    return hashlib.sha256(json_str.encode('utf-8')).hexdigest()

def get_checkpoint_filepath(json_str):
    """根据JSON哈希值生成检查点文件的路径"""
    input_hash = get_input_hash(json_str)
    return os.path.join(CHECKPOINT_DIR, f"checkpoint_{input_hash}.json")

def save_checkpoint_to_file(json_str, data_to_save):
    """保存检查点数据到本地文件"""
    ensure_checkpoint_dir_exists()
    filepath = get_checkpoint_filepath(json_str)
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        print(f"[DEBUG] Checkpoint saved to {filepath}") # For live log
        return True
    except Exception as e:
        st.error(f"保存检查点文件 '{filepath}' 失败: {e}")
        print(f"[ERROR] Failed to save checkpoint to {filepath}: {e}", file=sys.stderr)
        return False

def load_checkpoint_from_file(json_str):
    """从本地文件加载检查点数据"""
    filepath = get_checkpoint_filepath(json_str)
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"[DEBUG] Checkpoint loaded from {filepath}") # For live log
            return data
        except Exception as e:
            st.error(f"加载检查点文件 '{filepath}' 失败: {e}. 文件可能已损坏或格式不正确。建议删除后重试。")
            print(f"[ERROR] Failed to load checkpoint from {filepath}: {e}", file=sys.stderr)
            return None
    return None

def delete_checkpoint_file(json_str):
    """删除指定的检查点文件"""
    filepath = get_checkpoint_filepath(json_str)
    if os.path.exists(filepath):
        try:
            os.remove(filepath)
            print(f"[DEBUG] Checkpoint file {filepath} deleted.") # For live log
            return True
        except Exception as e:
            st.error(f"删除检查点文件 '{filepath}' 失败: {e}")
            print(f"[ERROR] Failed to delete checkpoint file {filepath}: {e}", file=sys.stderr)
            return False
    return False

# --- Validator Step Helper Functions for Resume Logic ---
def reset_validator_state_for_fresh_start():
    """重置AI翻译校验步骤相关的会话状态，用于全新开始。"""
    st.session_state.validator_sample_ready = False
    st.session_state.validator_user_confirmed = False
    st.session_state.validator_should_stop = False
    st.session_state.validator_sample_df_for_display = None
    st.session_state.validator_aggregated_results = defaultdict(lambda: {
        "original_text": None, "translated_text": None,
        "standard_analysis": None, "rhyme_analysis": None,
        "literal_translation": None, "decomposition_analysis": None
    })
    st.session_state.output_files.pop(config.STEP_TRANSLATION_VALIDATION, None)
    
    st.session_state.validator_progress = {"completed": 0, "total": 0}
    st.session_state.validator_progress_check = {"completed": 0, "total": 0}
    st.session_state.validator_progress_literal = {"completed": 0, "total": 0}
    st.session_state.validator_progress_decompose = {"completed": 0, "total": 0}
    
    st.session_state.validator_checkpoint_counter = 0
    st.session_state.validator_checkpoint_results = {}
    
    st.session_state.validator_tasks_all = [] 
    st.session_state.validator_tasks_remaining = []
    # st.session_state.validator_input_json_str = None # 保留输入，除非用户明确想更改
    if 'validator_manual_upload_key' in st.session_state: # 确保key存在
        st.session_state.validator_manual_upload_key += 1 
    else:
        st.session_state.validator_manual_upload_key = 0


    st.session_state.validator_triggered = False
    st.session_state.validator_processing_active = False
    st.session_state.validator_batch_type = None
    st.session_state.validator_should_resume_processing = False # Important for resume logic
    
    # Resetting these sets, which are populated by run_validation_batch
    st.session_state.validator_items_check_completed = set()
    st.session_state.validator_items_literal_completed = set()
    st.session_state.validator_items_decompose_completed = set()
    st.session_state.validator_live_log = "" # Clear live log
    
    # Delete corresponding checkpoint file if input JSON is known
    if st.session_state.get("validator_input_json_str"):
        delete_checkpoint_file(st.session_state.validator_input_json_str)
        
    st.toast("AI翻译校验状态已重置 (包括本地检查点)。", icon="🧹")

# --- Log Callback Function ---
def log_to_session_state(message, log_key='validator_live_log'):
    """安全地将日志消息追加到 st.session_state 中的指定键。"""
    print(f"[DEBUG_LOG_CALLBACK] Called with message: '{str(message)[:100]}...'") # Console print
    try:
        current_log_content = st.session_state.get(log_key, "") # Get current content for debugging
        st.session_state[log_key] = current_log_content + str(message) + "\n"
        print(f"[DEBUG_LOG_CALLBACK] Successfully updated session_state.'{log_key}'. New length: {len(st.session_state[log_key])}") # Console print
    except AttributeError as e_attr:
        # ScriptRunContext likely missing (e.g., browser closed)
        print(f"[LOG_CALLBACK_FALLBACK] AttributeError: {e_attr}. Message: {message}") # Console print
    except Exception as e:
        print(f"[LOG_CALLBACK_ERROR] Failed to write to session log: {e}. Message: {message}") # Console print

def repopulate_validator_state_from_checkpoint():
    """从检查点数据恢复聚合结果和进度统计。"""
    st.session_state.validator_aggregated_results = defaultdict(lambda: {
        "original_text": None, "translated_text": None,
        "standard_analysis": None, "rhyme_analysis": None,
        "literal_translation": None, "decomposition_analysis": None
    })

    completed_check_items = set()
    completed_literal_items = set()
    completed_decompose_items = set()
    
    # Build a lookup for original/translated text from validator_tasks_all
    # This assumes validator_tasks_all has been populated before this function is called.
    items_data_from_tasks_all = {task['key']: task['item'] for task in st.session_state.get('validator_tasks_all', [])}

    for key, data in st.session_state.validator_checkpoint_results.items():
        agg_data = st.session_state.validator_aggregated_results[key]
        item_master_data = items_data_from_tasks_all.get(key, {})
        agg_data['original_text'] = item_master_data.get("原文", "")
        agg_data['translated_text'] = item_master_data.get("译文", "")

        if data.get('standard_analysis_parsed'):
            agg_data['standard_analysis'] = data['standard_analysis_parsed']
            completed_check_items.add(key)
        if data.get('rhyme_analysis_parsed'): # Checkpoint stores them separately if they exist
            agg_data['rhyme_analysis'] = data['rhyme_analysis_parsed']
            completed_check_items.add(key) # Counted towards 'check' progress
        if data.get('literal_translation'):
            agg_data['literal_translation'] = data['literal_translation']
            completed_literal_items.add(key)
        if data.get('decomposition_analysis'):
            agg_data['decomposition_analysis'] = data['decomposition_analysis']
            completed_decompose_items.add(key)

    # Update progress counters based on ALL tasks defined in validator_tasks_all
    if 'validator_tasks_all' in st.session_state and st.session_state.validator_tasks_all:
        num_unique_items_total = len(set(t['key'] for t in st.session_state.validator_tasks_all))
        total_validation_prompts = len(st.session_state.validator_tasks_all) # Total prompts (std + rhyme)

        st.session_state.validator_progress_check["completed"] = len(completed_check_items)
        st.session_state.validator_progress_check["total"] = num_unique_items_total

        st.session_state.validator_progress_literal["completed"] = len(completed_literal_items)
        st.session_state.validator_progress_literal["total"] = num_unique_items_total if st.session_state.get('validator_enable_literal_translation', True) else 0
        
        st.session_state.validator_progress_decompose["completed"] = len(completed_decompose_items)
        st.session_state.validator_progress_decompose["total"] = num_unique_items_total if st.session_state.get('validator_enable_decomposition', True) else 0

        # Calculate completed prompts for the main progress bar
        # Each entry in checkpoint_results can correspond to multiple actual results (std_analysis, rhyme_analysis, literal, decompose)
        # We need to count how many *prompts* (standard/rhyme) are effectively done.
        # The `run_validation_batch` counts `completed_validation_tasks_in_batch` based on submitted tasks.
        # For resuming, we set st.session_state.validator_progress["completed"] based on what's in checkpoints.
        
        completed_prompt_tasks_from_checkpoint = 0
        for task in st.session_state.validator_tasks_all:
            task_key = task['key']
            task_type = task['type'] # 'standard' or 'rhyme'
            checkpoint_task_data = st.session_state.validator_checkpoint_results.get(task_key, {})
            
            if task_type == 'standard' and checkpoint_task_data.get('standard_analysis_parsed') is not None:
                completed_prompt_tasks_from_checkpoint += 1
            elif task_type == 'rhyme' and checkpoint_task_data.get('rhyme_analysis_parsed') is not None:
                completed_prompt_tasks_from_checkpoint += 1
        
        st.session_state.validator_progress["completed"] = completed_prompt_tasks_from_checkpoint
        st.session_state.validator_progress["total"] = total_validation_prompts
        
        # These are used by run_validation_batch's progress UI update logic
        st.session_state.validator_items_check_completed = completed_check_items
        st.session_state.validator_items_literal_completed = completed_literal_items
        st.session_state.validator_items_decompose_completed = completed_decompose_items

    st.toast("已从检查点恢复聚合结果和进度。", icon="🔄")


# --- Streamlit 会话状态初始化 ---
def init_session_state():
    if 'current_step_index' not in st.session_state:
        st.session_state.current_step_index = 0
    if 'step_status' not in st.session_state:
        st.session_state.step_status = {step: "pending" for step in config.WORKFLOW_STEPS} # pending, active, completed, failed
    if 'output_files' not in st.session_state:
        st.session_state.output_files = {} # To store paths or BytesIO objects of generated files
    if 'api_keys' not in st.session_state:
        st.session_state.api_keys = {"qwen": config.API_KEY, "deepseek": config.API_KEY} # 使用固定的API key
    
    # Preprocessor specific
    if 'excel_columns' not in st.session_state:
        st.session_state.excel_columns = []
    if 'column_mappings' not in st.session_state:
        st.session_state.column_mappings = {}
    if 'preprocess_triggered' not in st.session_state:
        st.session_state.preprocess_triggered = False
    if 'preprocess_uploaded_file_bytes' not in st.session_state:
        st.session_state.preprocess_uploaded_file_bytes = None
    if 'preprocess_output_json_name' not in st.session_state:
        st.session_state.preprocess_output_json_name = "processed_data.json"

    # Rich text check specific
    if 'rich_text_original_key' not in st.session_state:
        st.session_state.rich_text_original_key = "原文"
    if 'rich_text_translated_key' not in st.session_state:
        st.session_state.rich_text_translated_key = "译文"
    if 'rich_text_check_triggered' not in st.session_state:
        st.session_state.rich_text_check_triggered = False
    if 'rich_text_input_json_str' not in st.session_state:
        st.session_state.rich_text_input_json_str = None
    if 'rich_text_output_excel_name' not in st.session_state:
        st.session_state.rich_text_output_excel_name = "rich_text_check_report.xlsx"
    if 'rich_text_manual_upload_key' not in st.session_state: # Added for uploader reset
        st.session_state.rich_text_manual_upload_key = 0

    # Validator specific state
    if 'validator_sample_ready' not in st.session_state: 
        st.session_state.validator_sample_ready = False
    if 'validator_user_confirmed' not in st.session_state:
        st.session_state.validator_user_confirmed = False
    if 'validator_should_stop' not in st.session_state:
        st.session_state.validator_should_stop = False
    if 'validator_processed_keys' not in st.session_state: # Retained for potential future use, though run_validation_batch manages its own
        st.session_state.validator_processed_keys = set()
    if 'validator_aggregated_results' not in st.session_state:
        st.session_state.validator_aggregated_results = defaultdict(lambda: {
            "original_text": None, "translated_text": None,
            "standard_analysis": None, "rhyme_analysis": None,
            "literal_translation": None, 
            "decomposition_analysis": None 
        })
    if 'validator_progress' not in st.session_state: # Overall progress for all tasks (std + rhyme)
        st.session_state.validator_progress = {"completed": 0, "total": 0}
    if 'validator_progress_lock' not in st.session_state:
        st.session_state.validator_progress_lock = threading.Lock()
    if 'validator_autosave_enabled' not in st.session_state:
        st.session_state.validator_autosave_enabled = True
    if 'validator_checkpoint_counter' not in st.session_state: # Tracks completed tasks for resuming
        st.session_state.validator_checkpoint_counter = 0
    if 'validator_checkpoint_results' not in st.session_state: # Stores results of completed tasks for resuming
        st.session_state.validator_checkpoint_results = {}
    if 'validator_last_progress_update' not in st.session_state:
        st.session_state.validator_last_progress_update = time.time()
    
    # API Config specific state (fixed API config) 
    if 'qwen_api_url' not in st.session_state:
        st.session_state.qwen_api_url = config.QWEN3_BASE_URL_VALIDATOR
    if 'qwen_model_name' not in st.session_state:
        st.session_state.qwen_model_name = config.QWEN3_MODEL_VALIDATOR
    if 'deepseek_api_url' not in st.session_state:
        st.session_state.deepseek_api_url = config.DEEPSEEK_BASE_URL_VALIDATOR
    if 'deepseek_model_name' not in st.session_state:
        st.session_state.deepseek_model_name = config.DEEPSEEK_MODEL_VALIDATOR

    # Literal Translator (integrated into Validator) specific state for API config
    if 'literal_translator_model_name' not in st.session_state:
        st.session_state.literal_translator_model_name = config.LITERAL_TRANSLATOR_MODEL
    if 'literal_translator_base_url' not in st.session_state:
        st.session_state.literal_translator_base_url = config.LITERAL_TRANSLATOR_BASE_URL

    # Translation Decomposition (integrated into Validator) specific state for API config
    if 'decomposition_model_name' not in st.session_state:
        st.session_state.decomposition_model_name = config.DECOMPOSITION_MODEL
    if 'decomposition_base_url' not in st.session_state:
        st.session_state.decomposition_base_url = config.DECOMPOSITION_BASE_URL

    # Validator process control
    if 'validator_triggered' not in st.session_state:
        st.session_state.validator_triggered = False
    if 'validator_processing_active' not in st.session_state: 
        st.session_state.validator_processing_active = False
    if 'validator_batch_type' not in st.session_state: 
        st.session_state.validator_batch_type = None
    if 'validator_tasks_all' not in st.session_state: 
        st.session_state.validator_tasks_all = []
    if 'validator_tasks_remaining' not in st.session_state: 
        st.session_state.validator_tasks_remaining = []
    if 'validator_input_json_str' not in st.session_state:
        st.session_state.validator_input_json_str = None
    if 'validator_output_excel_name' not in st.session_state:
        st.session_state.validator_output_excel_name = "translation_validation_report.xlsx"
    if 'validator_manual_upload_key' not in st.session_state: # Added for uploader reset
        st.session_state.validator_manual_upload_key = 0

    # AI翻译校验步骤中集成模块的开关
    if 'validator_enable_literal_translation' not in st.session_state:
        st.session_state.validator_enable_literal_translation = True 
    if 'validator_enable_decomposition' not in st.session_state:
        st.session_state.validator_enable_decomposition = True

    # 为AI校验的子模块创建独立的进度跟踪 (for unique items)
    if 'validator_progress_check' not in st.session_state: # For main validation per item
        st.session_state.validator_progress_check = {"completed": 0, "total": 0}
    if 'validator_progress_literal' not in st.session_state: # For literal translation per item
        st.session_state.validator_progress_literal = {"completed": 0, "total": 0}
    if 'validator_progress_decompose' not in st.session_state: # For decomposition per item
        st.session_state.validator_progress_decompose = {"completed": 0, "total": 0}
    if 'validator_should_resume_processing' not in st.session_state: # For resume logic
        st.session_state.validator_should_resume_processing = False
    if 'validator_items_check_completed' not in st.session_state:
        st.session_state.validator_items_check_completed = set()
    if 'validator_items_literal_completed' not in st.session_state:
        st.session_state.validator_items_literal_completed = set()
    if 'validator_items_decompose_completed' not in st.session_state:
        st.session_state.validator_items_decompose_completed = set()

    # Live log for validator
    if 'validator_live_log' not in st.session_state: # Added for live log
        st.session_state.validator_live_log = "" # Added for live log
    if 'validator_current_activity' not in st.session_state: # For detailed activity
        st.session_state.validator_current_activity = "空闲"

    # Scoring specific
    if 'scoring_triggered' not in st.session_state:
        st.session_state.scoring_triggered = False
    if 'scoring_input_df_bytes' not in st.session_state: 
        st.session_state.scoring_input_df_bytes = None
    if 'scoring_output_excel_name' not in st.session_state:
        st.session_state.scoring_output_excel_name = "translation_scored_report.xlsx"
    if 'scoring_manual_upload_key' not in st.session_state: # Added for uploader reset
        st.session_state.scoring_manual_upload_key = 0


init_session_state() # Call initialization once

# Ensure locks are defined after init_session_state might have created them
if 'validator_progress_lock' not in st.session_state: 
    st.session_state.validator_progress_lock = threading.Lock()
# Locks for standalone modules (literal_translator_progress_lock, decomposition_progress_lock) are removed
# as those modules are now integrated or their standalone UIs are commented out.

# MOVED run_validation_batch function definition here
# NEW: Function to run a batch of validation tasks
def run_validation_batch(api_clients, tasks_to_run, qwen_model, deepseek_model):
    #print("[DEBUG] Entering run_validation_batch") # DEBUG Entry
    try:
        if 'validator_current_activity' in st.session_state: 
            st.session_state.validator_current_activity = "批处理任务初始化中..." 
        else:
            st.session_state.validator_current_activity = "准备处理..."
        if 'validator_live_log' not in st.session_state:
            st.session_state.validator_live_log = ""
    except AttributeError:
        print("[UI_FALLBACK] Failed to set initial activity/log in run_validation_batch due to missing ScriptRunContext.")

    # --- 后台日志显示 ---
    st.markdown("--- 后台日志 ---")
    # Define the text_area once. Its value will be updated when Streamlit reruns 
    # and st.session_state.validator_live_log has changed.
    st.text_area("实时日志输出:", 
                 value=st.session_state.get("validator_live_log", ""), 
                 height=200, 
                 key="validator_log_display_static", # Changed key to avoid conflict and denote its static definition
                 disabled=True, 
                 help="这里会显示处理过程中的详细信息和潜在错误。日志会自动追加，可手动清除。")
    if st.button("清除日志显示", key="clear_validator_log_button_static"):
        st.session_state.validator_live_log = ""
        st.rerun() 
    # --- 结束 后台日志显示 ---

    # Initialize API clients for literal translation and decomposition
    literal_translator_api_client = None
    if st.session_state.get('validator_enable_literal_translation', False):
        try:
            if st.session_state.literal_translator_base_url and st.session_state.api_keys.get("qwen"):
                literal_translator_api_client = OpenAI(
                    api_key=st.session_state.api_keys["qwen"],
                    base_url=st.session_state.literal_translator_base_url
                )
        except Exception as client_init_e:
            st.toast(f"初始化中英直译API客户端失败: {client_init_e}，该步骤将被跳过。", icon="⚠️") # Corrected potential escape issue

    decomposition_api_client = None
    if st.session_state.get('validator_enable_decomposition', False):
        try:
            if st.session_state.decomposition_base_url and st.session_state.api_keys.get("qwen"):
                decomposition_api_client = OpenAI(
                    api_key=st.session_state.api_keys["qwen"],
                    base_url=st.session_state.decomposition_base_url
                )
        except Exception as client_init_e:
            st.toast(f"初始化翻译拆解API客户端失败: {client_init_e}，该步骤将被跳过。", icon="⚠️") # Corrected potential escape issue

    processed_item_keys_in_batch = set()
    batch_id = f"batch_{time.strftime('%Y%m%d_%H%M%S')}_{hash(frozenset([t.get('key', '') for t in tasks_to_run])) % 10000}"
    st.session_state[f"validator_batch_{batch_id}_started"] = True

    global_completed_before_batch = st.session_state.validator_progress.get("completed", 0)
    global_total_validation_tasks = st.session_state.validator_progress.get("total", 0)


    progress_ui = {}
    st.markdown("---整体校验任务进度---")
    # Ensure total is available for initial display
    overall_total = st.session_state.validator_progress.get("total", 0)
    progress_ui['main_validation_tasks'] = {"bar": st.progress(0), "text": st.empty()}
    progress_ui['main_validation_tasks']['text'].text(f"总体验证任务进度: 0/{overall_total} (0%)")
    progress_ui['current_activity_display'] = st.empty() # Placeholder for current activity

    st.markdown("---各模块处理进度 (基于独立条目)---")
    
    check_total = st.session_state.validator_progress_check.get("total", 0)
    progress_ui['check_items'] = {"bar": st.progress(0), "text": st.empty()}
    progress_ui['check_items']['text'].text(f"翻译校验(条目): 0/{check_total} (0%)")

    if st.session_state.get('validator_enable_literal_translation', False):
        literal_total = st.session_state.validator_progress_literal.get("total", 0)
        progress_ui['literal_items'] = {"bar": st.progress(0), "text": st.empty()}
        progress_ui['literal_items']['text'].text(f"中英直译(条目): 0/{literal_total} (0%)")
    if st.session_state.get('validator_enable_decomposition', False):
        decompose_total = st.session_state.validator_progress_decompose.get("total", 0)
        progress_ui['decompose_items'] = {"bar": st.progress(0), "text": st.empty()}
        progress_ui['decompose_items']['text'].text(f"翻译拆解(条目): 0/{decompose_total} (0%)")

    item_sub_task_results = defaultdict(lambda: {
        'results': {}, 
        'item_data': None, 
        'expected_tasks_types': set(), 
        'completed_tasks_types': set()
    })
    batch_errors = [] # Initialize list to store errors from this batch

    unique_items_for_submission = {} 
    for task_info in tasks_to_run: 
        key = task_info['key']
        item_d = task_info['item']
        if key not in unique_items_for_submission:
            unique_items_for_submission[key] = {'item_data': item_d, 'validation_prompts': []}
        unique_items_for_submission[key]['validation_prompts'].append({'type': task_info['type'], 'prompt': task_info['prompt']})

    for key, data in unique_items_for_submission.items():
        item_sub_task_results[key]['item_data'] = data['item_data']
        for val_prompt_info in data['validation_prompts']:
             item_sub_task_results[key]['expected_tasks_types'].add(val_prompt_info['type']) 
        if st.session_state.get('validator_enable_literal_translation', False) and literal_translator_api_client and data['item_data'].get("译文", ""):
            item_sub_task_results[key]['expected_tasks_types'].add('literal')
        if st.session_state.get('validator_enable_decomposition', False) and decomposition_api_client and data['item_data'].get("译文", ""):
            item_sub_task_results[key]['expected_tasks_types'].add('decompose')
            
    temp_checkpoint_results_for_batch = {} 
    completed_validation_tasks_in_batch = 0
    
    # CRITICAL CHANGE FOR DEBUGGING: Reduce max_workers to 1
    max_w = 10
     # #print(f"[DEBUG] ThreadPoolExecutor max_workers = {max_w} (FORCED TO 1 FOR DEBUGGING)")


    try:
        with ThreadPoolExecutor(max_workers=max_w) as executor: 
            futures_registry = {} 

            # Helper function to safely update UI or print to console
            def safe_ui_update(ui_element, update_type, value=None, text_content=None):
                try:
                    if ui_element:
                        if update_type == "progress":
                            ui_element.progress(value)
                        elif update_type == "text":
                            ui_element.text(text_content)
                        elif update_type == "info":
                            ui_element.info(text_content)
                        elif update_type == "empty":
                            ui_element.empty()
                except AttributeError:
                    # Likely ScriptRunContext missing (browser closed)
                    if text_content:
                        print(f"[UI_UPDATE_FALLBACK] {text_content}")
                    elif value is not None:
                        print(f"[UI_UPDATE_FALLBACK] Progress: {value}")

            def safe_toast(message, icon=None):
                try:
                    st.toast(message, icon=icon)
                except AttributeError:
                    print(f"[TOAST_FALLBACK] {message} (Icon: {icon})")

            for key, data in unique_items_for_submission.items():
                item_data_for_submit = data['item_data']
                translated_text_for_submit = item_data_for_submit.get("译文", "")

                # A. Validation tasks (standard and/or rhyme)
                for val_prompt_info in data['validation_prompts']:
                    task_type = val_prompt_info['type']
                    prompt = val_prompt_info['prompt']
                    model_for_val = qwen_model if task_type == 'standard' else deepseek_model
                    
                    parsed_val_from_checkpoint = st.session_state.validator_checkpoint_results.get(key, {}).get(task_type + '_analysis_parsed')
                    
                    if parsed_val_from_checkpoint is not None:
                        item_sub_task_results[key]['results'][task_type] = parsed_val_from_checkpoint
                        item_sub_task_results[key]['completed_tasks_types'].add(task_type)
                        completed_validation_tasks_in_batch +=1 
                         # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Key {key}, Task {task_type}: Result loaded from checkpoint.")
                    else:
                         # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Key {key}, Task {task_type}: Submitting to executor.")
                        fut_val = executor.submit(translation_validator.analyze_translation_validator, api_clients, key, item_data_for_submit, prompt, task_type, model_for_val, log_to_session_state) # Pass log_callback
                        futures_registry[fut_val] = {'key': key, 'type': task_type, 'submit_time': time.time()}

                # B. Literal Translation task
                if st.session_state.get('validator_enable_literal_translation', False) and literal_translator_api_client and translated_text_for_submit:
                    lit_from_checkpoint = st.session_state.validator_checkpoint_results.get(key, {}).get('literal_translation')
                    if lit_from_checkpoint is not None:
                        item_sub_task_results[key]['results']['literal'] = lit_from_checkpoint
                        item_sub_task_results[key]['completed_tasks_types'].add('literal')
                         # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Key {key}, Task literal: Result loaded from checkpoint.")
                    else:
                         # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Key {key}, Task literal: Submitting to executor.")
                        fut_lit = executor.submit(literal_translator.translate_text_literal, literal_translator_api_client, st.session_state.literal_translator_model_name, translated_text_for_submit)
                        futures_registry[fut_lit] = {'key': key, 'type': 'literal', 'submit_time': time.time()}
                
                # C. Decomposition task
                if st.session_state.get('validator_enable_decomposition', False) and decomposition_api_client and translated_text_for_submit:
                    dec_from_checkpoint = st.session_state.validator_checkpoint_results.get(key, {}).get('decomposition_analysis')
                    if dec_from_checkpoint is not None:
                        item_sub_task_results[key]['results']['decompose'] = dec_from_checkpoint
                        item_sub_task_results[key]['completed_tasks_types'].add('decompose')
                        # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Key {key}, Task decompose: Result loaded from checkpoint.")
                    elif len(translated_text_for_submit) > 200:
                        skip_message = "译文超长 (200字以上)，已跳过拆解"
                        item_sub_task_results[key]['results']['decompose'] = skip_message
                        item_sub_task_results[key]['completed_tasks_types'].add('decompose') # Mark as 'completed' as we've handled it
                        temp_checkpoint_results_for_batch.setdefault(key, {})['decomposition_analysis'] = skip_message
                        # Consider adding a toast or log if you want to notify user about the skip actively
                        # st.toast(f"条目 {key}: {skip_message}", icon="ℹ️") 
                    else: # Not from checkpoint and length is suitable
                        # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Key {key}, Task decompose: Submitting to executor.")
                        fut_dec = executor.submit(translation_decomposer.explain_translation_text_decomposition_adapted, decomposition_api_client, st.session_state.decomposition_model_name, translated_text_for_submit)
                        futures_registry[fut_dec] = {'key': key, 'type': 'decompose', 'submit_time': time.time()}
                elif st.session_state.get('validator_enable_decomposition', False) and \
                     'decompose' in item_sub_task_results[key]['expected_tasks_types'] and \
                     not (decomposition_api_client and translated_text_for_submit):
                    # If decomposition was expected but cannot run (e.g., API client failed, or no text after all)
                    # and not already handled by checkpoint or length check.
                    skip_message = "翻译拆解跳过 (客户端或文本缺失)"
                    if not translated_text_for_submit : skip_message = "翻译拆解跳过 (译文为空)"

                    item_sub_task_results[key]['results']['decompose'] = skip_message
                    item_sub_task_results[key]['completed_tasks_types'].add('decompose')
                    temp_checkpoint_results_for_batch.setdefault(key, {})['decomposition_analysis'] = skip_message

             # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - All tasks submitted. Waiting for completion. Total futures: {len(futures_registry)}")
            for future in as_completed(futures_registry):
                if st.session_state.validator_should_stop: 
                     # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Validator should stop. Breaking as_completed loop.")
                    break
                
                info = futures_registry[future]
                key = info['key']
                task_type = info['type']
                submit_time = info['submit_time']
                completion_time = time.time()
                duration = completion_time - submit_time
                 # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Future completed for Key {key}, Task {task_type}. Duration: {duration:.2f}s")

                try:
                    raw_result = future.result(timeout=60) # Added timeout
                    
                    if task_type == 'standard' or task_type == 'rhyme':
                        _k, _ot, _tt, analysis_raw, status, _detail, toast_msg = raw_result
                        if toast_msg: safe_toast(toast_msg) # Use safe_toast
                        
                        parsed_analysis = translation_validator.parse_and_categorize_analysis_validator(analysis_raw) if status == "success" else (analysis_raw, True, -1)
                        item_sub_task_results[key]['results'][task_type] = parsed_analysis
                        temp_checkpoint_results_for_batch.setdefault(key, {})[task_type + '_analysis_parsed'] = parsed_analysis
                        completed_validation_tasks_in_batch += 1
                    
                    elif task_type == 'literal':
                        item_sub_task_results[key]['results']['literal'] = raw_result
                        temp_checkpoint_results_for_batch.setdefault(key, {})['literal_translation'] = raw_result
                        if isinstance(raw_result, str) and raw_result.startswith("中英直译错误:"): safe_toast(f"条目 {key}: {raw_result}", icon="⚠️") # Use safe_toast
                   
                    elif task_type == 'decompose':
                        item_sub_task_results[key]['results']['decompose'] = raw_result
                        temp_checkpoint_results_for_batch.setdefault(key, {})['decomposition_analysis'] = raw_result
                        if isinstance(raw_result, str) and raw_result.startswith("翻译拆解错误:"): safe_toast(f"条目 {key}: {raw_result}", icon="⚠️") # Use safe_toast

                    item_sub_task_results[key]['completed_tasks_types'].add(task_type)

                    current_item_info = item_sub_task_results[key]
                    if current_item_info['completed_tasks_types'] == current_item_info['expected_tasks_types']:
                        agg_data = st.session_state.validator_aggregated_results.setdefault(key, defaultdict(lambda: None))
                        item_data_ctx = current_item_info['item_data']
                        agg_data['original_text'] = item_data_ctx.get("原文", "")
                        agg_data['translated_text'] = item_data_ctx.get("译文", "")
                        agg_data['standard_analysis'] = current_item_info['results'].get('standard')
                        agg_data['rhyme_analysis'] = current_item_info['results'].get('rhyme')
                        agg_data['literal_translation'] = current_item_info['results'].get('literal')
                        agg_data['decomposition_analysis'] = current_item_info['results'].get('decompose')
                        processed_item_keys_in_batch.add(key)

                        with st.session_state.validator_progress_lock:
                            if 'standard' in current_item_info['completed_tasks_types'] or 'rhyme' in current_item_info['completed_tasks_types']:
                                if key not in st.session_state.get('validator_items_check_completed', set()):
                                    st.session_state.validator_progress_check["completed"] = st.session_state.validator_progress_check.get("completed",0) + 1
                                    st.session_state.setdefault('validator_items_check_completed', set()).add(key)
                            if 'literal' in current_item_info['completed_tasks_types']:
                                if key not in st.session_state.get('validator_items_literal_completed', set()):
                                    st.session_state.validator_progress_literal["completed"] = st.session_state.validator_progress_literal.get("completed",0) + 1
                                    st.session_state.setdefault('validator_items_literal_completed', set()).add(key)
                            if 'decompose' in current_item_info['completed_tasks_types']:
                                if key not in st.session_state.get('validator_items_decompose_completed', set()):
                                    st.session_state.validator_progress_decompose["completed"] = st.session_state.validator_progress_decompose.get("completed",0) + 1
                                    st.session_state.setdefault('validator_items_decompose_completed', set()).add(key)
                
                except TimeoutError: # Added TimeoutError handling
                    info = futures_registry[future]
                    key = info['key']
                    task_type = info['type']
                    print(f"[TIMEOUT_DEBUG] {time.strftime('%H:%M:%S')} - Key {key}, Task {task_type}: 处理超时 (60s)")
                    safe_toast(f"任务 {key} (类型: {task_type}) 处理超时。", icon="⚠️") # Use safe_toast
                    batch_errors.append({"key": key, "type": task_type, "error": "处理超时 (60s)", "details": "TimeoutError"}) # Collect error
                    # Store error info in results
                    error_message = "错误: 处理超时 (60秒)"
                    if task_type == 'standard' or task_type == 'rhyme':
                        item_sub_task_results[key]['results'][task_type] = (error_message, True, -1)
                        temp_checkpoint_results_for_batch.setdefault(key, {})[task_type + '_analysis_parsed'] = (error_message, True, -1)
                        # We don't increment completed_validation_tasks_in_batch here as the task didn't truly complete with valid data
                    elif task_type == 'literal':
                        item_sub_task_results[key]['results']['literal'] = error_message
                        temp_checkpoint_results_for_batch.setdefault(key, {})['literal_translation'] = error_message
                    elif task_type == 'decompose':
                        item_sub_task_results[key]['results']['decompose'] = error_message
                        temp_checkpoint_results_for_batch.setdefault(key, {})['decomposition_analysis'] = error_message
                    item_sub_task_results[key]['completed_tasks_types'].add(task_type) # Mark as completed to update UI for sub-tasks
                    # Update progress for sub-modules to reflect an attempt was made
                    with st.session_state.validator_progress_lock:
                        if task_type == 'standard' or task_type == 'rhyme':
                            if key not in st.session_state.get('validator_items_check_completed', set()):
                                st.session_state.validator_progress_check["completed"] = st.session_state.validator_progress_check.get("completed",0) + 1
                                st.session_state.setdefault('validator_items_check_completed', set()).add(key)
                        elif task_type == 'literal':
                             if key not in st.session_state.get('validator_items_literal_completed', set()):
                                st.session_state.validator_progress_literal["completed"] = st.session_state.validator_progress_literal.get("completed",0) + 1
                                st.session_state.setdefault('validator_items_literal_completed', set()).add(key)
                        elif task_type == 'decompose':
                            if key not in st.session_state.get('validator_items_decompose_completed', set()):
                                st.session_state.validator_progress_decompose["completed"] = st.session_state.validator_progress_decompose.get("completed",0) + 1
                                st.session_state.setdefault('validator_items_decompose_completed', set()).add(key)
                except Exception as exc_inner:
                     #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - ERROR processing future for Key {key}, Task {task_type}: {exc_inner}") # DEBUG Error
                    st.error(f"处理校验子任务 (Key: {key}, Type: {task_type}) 时发生错误: {exc_inner}") # This st.error might fail if browser closed, consider safe_toast or print for critical errors
                    print(f"[STDERR_DEBUG] {time.strftime('%H:%M:%S')} - ERROR processing future for Key {key}, Task {task_type}: {exc_inner}", file=sys.stderr)
                    batch_errors.append({"key": key, "type": task_type, "error": str(exc_inner)[:200], "details": traceback.format_exc()[:500]}) # Collect error
                    import traceback
                    traceback.print_exc(file=sys.stderr)
                    item_sub_task_results[key]['results'][task_type] = (f"错误: {exc_inner}", True, -1) if task_type in ['standard', 'rhyme'] else f"错误: {exc_inner}"
                    item_sub_task_results[key]['completed_tasks_types'].add(task_type)

                current_main_val_tasks_done = global_completed_before_batch + completed_validation_tasks_in_batch
                st.session_state.validator_progress["completed"] = current_main_val_tasks_done
                prog_main_val = current_main_val_tasks_done / global_total_validation_tasks if global_total_validation_tasks > 0 else 0
                if 'main_validation_tasks' in progress_ui:
                    safe_ui_update(progress_ui['main_validation_tasks']["text"], "text", text_content=f"总体验证任务进度: {current_main_val_tasks_done}/{global_total_validation_tasks} ({prog_main_val:.0%})")
                    safe_ui_update(progress_ui['main_validation_tasks']["bar"], "progress", value=prog_main_val)

                # Update current activity display
                if 'current_activity_display' in progress_ui:
                    current_activity_text = st.session_state.get('validator_current_activity', "准备中...")
                    safe_ui_update(progress_ui['current_activity_display'], "info", text_content=f"当前操作: {current_activity_text}")

                current_time_loop = time.time() # Use a different variable name for time inside the loop
                if current_time_loop - st.session_state.validator_last_progress_update > 0.2: 
                    with st.session_state.validator_progress_lock: 
                        if 'check_items' in progress_ui and progress_ui['check_items']["text"] and progress_ui['check_items']["bar"]:
                            comp_c = st.session_state.validator_progress_check.get("completed",0)
                            tot_c = st.session_state.validator_progress_check.get("total",0)
                            prog_c = comp_c / tot_c if tot_c > 0 else 0
                            safe_ui_update(progress_ui['check_items']["text"], "text", text_content=f"翻译校验(条目): {comp_c}/{tot_c} ({prog_c:.0%})")
                            safe_ui_update(progress_ui['check_items']["bar"], "progress", value=prog_c)
                        
                        if 'literal_items' in progress_ui and progress_ui['literal_items']["text"] and progress_ui['literal_items']["bar"] and st.session_state.get('validator_enable_literal_translation', False):
                            comp_l = st.session_state.validator_progress_literal.get("completed",0)
                            tot_l = st.session_state.validator_progress_literal.get("total",0)
                            prog_l = comp_l / tot_l if tot_l > 0 else 0
                            safe_ui_update(progress_ui['literal_items']["text"], "text", text_content=f"中英直译(条目): {comp_l}/{tot_l} ({prog_l:.0%})")
                            safe_ui_update(progress_ui['literal_items']["bar"], "progress", value=prog_l)

                        if 'decompose_items' in progress_ui and progress_ui['decompose_items']["text"] and progress_ui['decompose_items']["bar"] and st.session_state.get('validator_enable_decomposition', False):
                            comp_d = st.session_state.validator_progress_decompose.get("completed",0)
                            tot_d = st.session_state.validator_progress_decompose.get("total",0)
                            prog_d = comp_d / tot_d if tot_d > 0 else 0
                            safe_ui_update(progress_ui['decompose_items']["text"], "text", text_content=f"翻译拆解(条目): {comp_d}/{tot_d} ({prog_d:.0%})")
                            safe_ui_update(progress_ui['decompose_items']["bar"], "progress", value=prog_d)
                    st.session_state.validator_last_progress_update = current_time_loop
                
                if st.session_state.validator_autosave_enabled and (current_time_loop - st.session_state.get('validator_last_autosave_time', 0) > 30): 
                    for key_cp, data_cp in temp_checkpoint_results_for_batch.items():
                        st.session_state.validator_checkpoint_results.setdefault(key_cp, {}).update(data_cp)
                    st.session_state.validator_checkpoint_counter = completed_validation_tasks_in_batch
                    # --- Save to file ---
                    if st.session_state.get("validator_input_json_str"):
                        checkpoint_data_to_save = {
                            "validator_checkpoint_results": dict(st.session_state.validator_checkpoint_results),
                            "validator_tasks_all": st.session_state.get("validator_tasks_all", []),
                            "validator_progress": st.session_state.get("validator_progress", {}),
                            "validator_progress_check": st.session_state.get("validator_progress_check", {}),
                            "validator_progress_literal": st.session_state.get("validator_progress_literal", {}),
                            "validator_progress_decompose": st.session_state.get("validator_progress_decompose", {}),
                            "validator_items_check_completed": list(st.session_state.get("validator_items_check_completed", set())),
                            "validator_items_literal_completed": list(st.session_state.get("validator_items_literal_completed", set())),
                            "validator_items_decompose_completed": list(st.session_state.get("validator_items_decompose_completed", set()))
                        }
                        save_checkpoint_to_file(st.session_state.validator_input_json_str, checkpoint_data_to_save)
                    # --- End save to file ---
                    st.session_state['validator_last_autosave_time'] = current_time_loop
                    safe_toast(f"自动保存校验进度 (包括本地文件)...", icon="💾") # Use safe_toast
            
             # #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - as_completed loop finished.") # DEBUG Loop End

        if st.session_state.validator_should_stop:
            st.warning("翻译校验处理已手动停止。") # This might fail if browser closed
            print("[INFO] 翻译校验处理已手动停止。") # Fallback print
            for key_cp, data_cp in temp_checkpoint_results_for_batch.items():
                st.session_state.validator_checkpoint_results.setdefault(key_cp, {}).update(data_cp)

    except Exception as e_outer: 
         #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - ERROR in ThreadPoolExecutor block: {e_outer}") # DEBUG Error
        st.error(f"运行翻译校验批处理时发生外部错误: {e_outer}") # This might fail
        print(f"[STDERR_DEBUG] {time.strftime('%H:%M:%S')} - ERROR in ThreadPoolExecutor block: {e_outer}", file=sys.stderr)
        import traceback
        st.error(f"错误详情: {traceback.format_exc()}") # This might fail
        traceback.print_exc(file=sys.stderr)
        for key_cp, data_cp in temp_checkpoint_results_for_batch.items():
            st.session_state.validator_checkpoint_results.setdefault(key_cp, {}).update(data_cp)
        return set(), batch_errors # Return empty set for keys and the errors
    finally: 
         #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Entering finally block of run_validation_batch.") # DEBUG Finally
        for key_cp, data_cp in temp_checkpoint_results_for_batch.items():
            st.session_state.validator_checkpoint_results.setdefault(key_cp, {}).update(data_cp)
        
        st.session_state.validator_checkpoint_counter = completed_validation_tasks_in_batch 
        # --- Final save to file ---
        if st.session_state.get("validator_input_json_str"):
            final_checkpoint_data_to_save = {
                "validator_checkpoint_results": dict(st.session_state.validator_checkpoint_results),
                "validator_tasks_all": st.session_state.get("validator_tasks_all", []),
                "validator_progress": st.session_state.get("validator_progress", {}),
                "validator_progress_check": st.session_state.get("validator_progress_check", {}),
                "validator_progress_literal": st.session_state.get("validator_progress_literal", {}),
                "validator_progress_decompose": st.session_state.get("validator_progress_decompose", {}),
                "validator_items_check_completed": list(st.session_state.get("validator_items_check_completed", set())),
                "validator_items_literal_completed": list(st.session_state.get("validator_items_literal_completed", set())),
                "validator_items_decompose_completed": list(st.session_state.get("validator_items_decompose_completed", set()))
            }
            save_checkpoint_to_file(st.session_state.validator_input_json_str, final_checkpoint_data_to_save)
            print("[DEBUG] Final checkpoint saved to file.")
        # --- End final save to file ---
        
        try:
            for p_ui_key in progress_ui:
                if progress_ui[p_ui_key].get("bar"):
                    safe_ui_update(progress_ui[p_ui_key]["bar"], "empty")
                if progress_ui[p_ui_key].get("text"):
                    safe_ui_update(progress_ui[p_ui_key]["text"], "empty")
                if p_ui_key == 'current_activity_display' and progress_ui[p_ui_key]: 
                     safe_ui_update(progress_ui[p_ui_key], "empty")
            # Clear log display placeholder at the end of the batch too
            st.session_state.validator_live_log = ""
        except Exception: 
            pass 
        st.session_state[f"validator_batch_{batch_id}_completed"] = True
         #print(f"[DEBUG] {time.strftime('%H:%M:%S')} - Exiting run_validation_batch") # DEBUG Exit

    return processed_item_keys_in_batch, batch_errors # Return processed keys and errors

# --- UI 辅助函数 ---
def display_step_indicators():
    cols = st.columns(len(config.WORKFLOW_STEPS))
    current_index = st.session_state.current_step_index

    def set_step_index(index):
        st.session_state.current_step_index = index

    for i, step_name in enumerate(config.WORKFLOW_STEPS):
        status = st.session_state.step_status.get(step_name, "pending")
        is_current = (i == current_index)
        
        button_label = step_name.split('. ')[1] 
        button_key = f"step_button_{i}"
        button_type = "primary" if is_current else "secondary"

        color = "#D3D3D3" 
        border_style = "1px solid #cccccc"
        if status == "active" or (is_current and status == "pending"): 
            color = "#ADD8E6" 
        elif status == "completed":
            color = "#90EE90" 
        elif status == "failed":
            color = "#F08080" 
        if is_current:
            border_style = "3px solid #0066CC" 

        with cols[i]:
            st.markdown(
                f"""
                <div style='
                    background-color:{color}; 
                    padding: 5px 10px;
                    border-radius: 5px; 
                    text-align:center; 
                    border: {border_style}; 
                    margin-bottom: 5px;
                    height: 30px; 
                    line-height: 20px; 
                    font-size: smaller;
                    color: black;
                '>
                    {status.capitalize()} 
                </div>
                """, unsafe_allow_html=True
            )
            if is_current:
                st.markdown(
                    f"""
                    <div style='width:100%'>
                    <button type='button' 
                        style='
                            width: 100%; 
                            background-color: #0066CC; 
                            color: white; 
                            padding: 8px 0px; 
                            border: none; 
                            border-radius: 5px; 
                            cursor: pointer;
                            font-weight: bold;
                        '
                        onclick='console.log("Button {i} clicked"); parent.postMessage({{"func":"st_button_click", "args":{{"key":"{button_key}"}}}},"*")'>
                        {button_label}
                    </button>
                    </div>
                    """, unsafe_allow_html=True
                )
            else:
                st.button(
                    button_label, 
                    key=button_key, 
                    on_click=set_step_index, 
                    args=(i,), 
                    type=button_type, 
                    use_container_width=True
                )

# --- 主应用 ---
st.set_page_config(layout="wide")
st.title("翻译自动化辅助处理平台")
st.markdown("---")

display_step_indicators()
st.markdown("---")

active_step_index = st.session_state.current_step_index
active_step_name = config.WORKFLOW_STEPS[active_step_index] if active_step_index < len(config.WORKFLOW_STEPS) else "流程结束"

# ------------------------------
# 步骤1: 数据预处理
# ------------------------------
if active_step_name == config.STEP_PREPROCESS:
    st.header(f":arrow_forward: {config.STEP_PREPROCESS}")

    if st.session_state.preprocess_triggered:
        if st.session_state.preprocess_uploaded_file_bytes:
            with st.spinner("正在进行数据预处理..."):
                file_bytes_io = io.BytesIO(st.session_state.preprocess_uploaded_file_bytes)
                processed_data_dict = preprocess.preprocess_data_core_adapted(
                    file_bytes_io,
                    st.session_state.column_mappings,
                    config.FILE_NAME_MAPPING_PREPROCESS,
                    config.EXPECTED_COLUMNS_CONFIG_PREPROCESS
                )

                if processed_data_dict:
                    json_output_str = json.dumps(processed_data_dict, ensure_ascii=False, indent=2)
                    output_json_name = st.session_state.preprocess_output_json_name
                    st.session_state.output_files[config.STEP_PREPROCESS] = {
                        "file_name": output_json_name,
                        "data": json_output_str,
                        "type": "json"
                    }
                    st.session_state.step_status[config.STEP_PREPROCESS] = "completed"
                    st.success("数据预处理完成！")
                else:
                    st.session_state.step_status[config.STEP_PREPROCESS] = "failed"
                    st.error("预处理未产生数据或失败。")
            st.session_state.preprocess_triggered = False
            st.session_state.preprocess_uploaded_file_bytes = None
            st.rerun()
        else:
            st.error("无法找到上传的文件数据，请重新上传。")
            st.session_state.preprocess_triggered = False
            st.session_state.step_status[config.STEP_PREPROCESS] = "failed"
            st.rerun()

    elif st.session_state.step_status.get(config.STEP_PREPROCESS) == "completed":
        st.info("数据预处理已完成。您可以下载文件或进入下一步。")
        utils.make_download_button(
            st.session_state.output_files[config.STEP_PREPROCESS]["data"],
            st.session_state.output_files[config.STEP_PREPROCESS]["file_name"],
            "下载预处理后的JSON"
        )
        col1_ppo, col2_ppo, col3_ppo = st.columns(3)
        with col1_ppo:
            if st.button("重新开始预处理", key="restart_preprocess_completed", type="secondary"):
                st.session_state.step_status[config.STEP_PREPROCESS] = "pending"
                st.session_state.output_files.pop(config.STEP_PREPROCESS, None)
                st.session_state.current_step_index = config.WORKFLOW_STEPS.index(config.STEP_PREPROCESS)
                if 'excel_columns' in st.session_state: del st.session_state.excel_columns
                if 'column_mappings' in st.session_state: st.session_state.column_mappings = {} # Reset, not delete
                st.session_state.preprocess_triggered = False
                st.session_state.preprocess_uploaded_file_bytes = None
                st.rerun()
        with col3_ppo:
            if st.button(f"➡️ 进入下一步：{config.STEP_RICH_TEXT_CHECK.split('. ')[1]}", key="proceed_from_preprocess", type="primary"):
                utils.complete_step_and_proceed(config.STEP_PREPROCESS, config.WORKFLOW_STEPS)

    elif st.session_state.step_status.get(config.STEP_PREPROCESS) == "failed":
         st.error("数据预处理失败或未产生数据。")
         col1_ppf, col2_ppf, col3_ppf = st.columns(3)
         with col1_ppf:
             if st.button("重试预处理", key="retry_preprocess_failed", type="secondary"):
                 st.session_state.step_status[config.STEP_PREPROCESS] = "pending"
                 st.session_state.output_files.pop(config.STEP_PREPROCESS, None)
                 st.session_state.preprocess_triggered = False
                 st.session_state.preprocess_uploaded_file_bytes = None
                 st.rerun()

    else: 
        st.session_state.step_status[config.STEP_PREPROCESS] = "active"
        uploaded_excel_file = st.file_uploader("上传原始Excel数据文件 (.xlsx, .xls)", type=["xlsx", "xls"], key="preprocess_uploader")

        if uploaded_excel_file:
            try:
                file_bytes = uploaded_excel_file.getvalue()
                st.session_state.preprocess_uploaded_file_bytes = file_bytes
                df_peek = pd.read_excel(io.BytesIO(file_bytes), nrows=0)
                st.session_state.excel_columns = df_peek.columns.tolist()
            except Exception as e:
                 st.error(f"读取Excel文件失败: {e}")
                 st.session_state.excel_columns = []
                 st.session_state.preprocess_uploaded_file_bytes = None

            if st.session_state.excel_columns and st.session_state.preprocess_uploaded_file_bytes:
                st.subheader("列映射配置")
                cols_map_ui = {} # Use a temporary dict for UI selection storage
                for logical_key, cfg in config.EXPECTED_COLUMNS_CONFIG_PREPROCESS.items():
                    label = f"{cfg['description']}{' (必填项)' if cfg['is_core'] else ''}"
                    default_selection = st.session_state.column_mappings.get(logical_key, cfg.get('default_name'))
                    
                    options = [""] + st.session_state.excel_columns
                    index = 0
                    if default_selection in options:
                       try: index = options.index(default_selection)
                       except ValueError: index = 0 
                    else: index = 0
                    
                    # Use a unique key for each selectbox to store its current selection
                    selectbox_key = f"map_select_{logical_key}"
                    
                    selected_value = st.selectbox(
                        label, 
                        options=options, 
                        index=index, 
                        key=selectbox_key,
                    )
                    # Update the live map for current selections
                    if selected_value:
                        cols_map_ui[logical_key] = selected_value
                    elif logical_key in cols_map_ui: # Remove if deselected (chose "")
                        del cols_map_ui[logical_key]
                    
                # Persist the current UI selections to session_state.column_mappings
                st.session_state.column_mappings = cols_map_ui.copy()
                

                output_json_name = st.text_input("输出JSON文件名:", value=st.session_state.preprocess_output_json_name)
                st.session_state.preprocess_output_json_name = output_json_name

                col1_start_pp, col2_start_pp, col3_start_pp = st.columns(3)
                with col3_start_pp:
                    if st.button("开始预处理", key="start_preprocess", type="primary"):
                        st.session_state.preprocess_triggered = True
                        st.rerun()

# ------------------------------
# 步骤2: 富文本检查
# ------------------------------
elif active_step_name == config.STEP_RICH_TEXT_CHECK:
    st.header(f":memo: {config.STEP_RICH_TEXT_CHECK}")

    if st.session_state.rich_text_check_triggered:
        json_data_str = st.session_state.get('rich_text_input_json_str')
        if json_data_str:
            with st.spinner("正在进行富文本检查..."):
                try:
                    st.info(f"开始处理JSON数据，原文键: {st.session_state.rich_text_original_key}, 译文键: {st.session_state.rich_text_translated_key}")
                    results_df, stats = rich_text_checker.run_rich_text_check_core(
                        json_data_str,
                                                          st.session_state.rich_text_original_key,
                        st.session_state.rich_text_translated_key
                    )
                    st.session_state.rich_text_check_results_df = results_df # Store for potential UI preview

                    is_valid_result = not results_df.empty or (hasattr(results_df, 'attrs') and results_df.attrs.get('valid_result', False))
                    output_excel_name = st.session_state.rich_text_output_excel_name
                    
                    print(f"[DEBUG] Rich Text Check: is_valid_result = {is_valid_result}") # 调试信息
                    if not results_df.empty:
                        print(f"[DEBUG] Rich Text Check: results_df is not empty, shape {results_df.shape}") # 调试信息
                    if hasattr(results_df, 'attrs') and results_df.attrs.get('valid_result', False):
                        print("[DEBUG] Rich Text Check: results_df has valid_result=True attribute") # 调试信息

                    if is_valid_result:
                        output_buffer = io.BytesIO()
                        with pd.ExcelWriter(output_buffer, engine='openpyxl') as writer:
                            results_df.to_excel(writer, index=False)
                        excel_bytes = output_buffer.getvalue()
                        st.session_state.output_files[config.STEP_RICH_TEXT_CHECK] = {
                            "file_name": output_excel_name,
                            "data": excel_bytes, 
                            "type": "excel_bytes", 
                            "stats": stats 
                        }
                        print(f"[DEBUG] Rich Text Check: Output file saved to session state for {config.STEP_RICH_TEXT_CHECK}") # 调试信息
                        
                        if stats["non_compliant"] > 0:
                            non_compliant_df = results_df[results_df["Result"] == "不合规"]
                            non_compliant_buffer = io.BytesIO()
                            with pd.ExcelWriter(non_compliant_buffer, engine='openpyxl') as writer:
                                non_compliant_df.to_excel(writer, index=False)
                            non_compliant_bytes = non_compliant_buffer.getvalue()
                            output_name_parts = os.path.splitext(output_excel_name)
                            non_compliant_excel_name = f"{output_name_parts[0]}_不合规{output_name_parts[1]}"
                            st.session_state.output_files[f"{config.STEP_RICH_TEXT_CHECK}_non_compliant"] = {
                                "file_name": non_compliant_excel_name,
                                "data": non_compliant_bytes,
                                "type": "excel_bytes"
                            }
                            
                        st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "completed"
                        st.success("富文本检查完成！")
                    else:
                        st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "failed"
                        st.error("富文本检查未产生结果或所有条目被跳过/失败。")
                        
                except Exception as e:
                    st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "failed"
                    st.error(f"富文本检查失败: {str(e)}")
                    import traceback
                    st.error(f"错误详情:\n{traceback.format_exc()}")

            st.session_state.rich_text_check_triggered = False
            st.session_state.rich_text_input_json_str = None 
            st.session_state.rich_text_manual_upload_key += 1 
            st.rerun() 
        else:
            st.error("富文本检查输入数据丢失，请返回上一步或重新上传。")
            st.session_state.rich_text_check_triggered = False
            st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "failed"
            st.session_state.rich_text_manual_upload_key += 1
            st.rerun()

    elif st.session_state.step_status.get(config.STEP_RICH_TEXT_CHECK) == "completed":
        st.info("富文本检查已完成。您可以下载报告或进入下一步。")
        
        # 显示富文本检查结果统计
        if config.STEP_RICH_TEXT_CHECK in st.session_state.output_files:
            file_info = st.session_state.output_files[config.STEP_RICH_TEXT_CHECK]
            if "stats" in file_info:
                stats = file_info["stats"]
                st.subheader("📊 富文本检查结果统计")
                
                total_processed_rt = stats.get("processed", 0) - stats.get("skipped", 0)
                if total_processed_rt > 0:
                    col_rt_stat1, col_rt_stat2 = st.columns(2)
                    with col_rt_stat1:
                        st.metric("总条目数", stats.get("processed", 0))
                        st.metric("有效条目数", total_processed_rt)
                        st.metric("跳过条目数", stats.get("skipped", 0))
                    with col_rt_stat2:
                        st.metric("合规条目数", stats.get("compliant", 0))
                        st.metric("不合规条目数", stats.get("non_compliant", 0), delta=-stats.get("non_compliant", 0) if stats.get("non_compliant", 0) > 0 else None, delta_color="inverse")
                        st.metric("处理失败条目数", stats.get("failed", 0), delta=-stats.get("failed", 0) if stats.get("failed", 0) > 0 else None, delta_color="inverse")

                    if stats.get("non_compliant", 0) == 0 and stats.get("failed", 0) == 0 and total_processed_rt > 0:
                        st.success(f"共{total_processed_rt}条有效条目，全部合规！")
                    elif stats.get("non_compliant", 0) > 0:
                        st.warning(f"共{total_processed_rt}条有效条目，其中{stats.get('non_compliant', 0)}条不合规。")
                    elif stats.get("failed", 0) > 0:
                        st.error(f"共{total_processed_rt}条有效条目，其中{stats.get('failed', 0)}条处理失败。")
                else:
                    st.info("所有条目均不包含需要检查的富文本标签，或有效输入数据为空，因此无统计数据显示。")
                st.markdown("---")
            
            # --- 显示不合规条目预览 ---
            if stats and stats.get("non_compliant", 0) > 0:
                st.subheader("⚠️ 富文本不合规条目预览")
                # 需要从 session_state 中获取完整的 results_df (它没有直接存在 output_files 的 stats 中)
                # run_rich_text_check_core 返回的是 results_df, 我们需要一种方式在它完成后访问它
                # 暂时假设如果 output_files[config.STEP_RICH_TEXT_CHECK] 存在，其 data 是 excel_bytes
                # 我们需要反向解析这个 excel_bytes 来获取 DataFrame，或者在保存时也保存原始df
                # 为了简化，如果文件存在，我们尝试读取它。更优的做法是在执行后临时保存df到session_state
                
                # 假设 rich_text_check_results_df 在成功执行后被临时存入 session_state
                if "rich_text_check_results_df" in st.session_state and isinstance(st.session_state.rich_text_check_results_df, pd.DataFrame):
                    non_compliant_df_preview = st.session_state.rich_text_check_results_df[st.session_state.rich_text_check_results_df["Result"] == "不合规"]
                    if not non_compliant_df_preview.empty:
                        st.dataframe(non_compliant_df_preview[["Index", "Result", "Details"]].head(10)) #预览前10条不合规
                    else:
                        st.info("统计显示有不合规条目，但未能提取预览。")
                else:
                    st.info("不合规条目预览需要访问处理后的DataFrame，当前未找到。可下载完整报告查看。")
            # --- 结束 不合规条目预览 ---
            
        main_report_exists = config.STEP_RICH_TEXT_CHECK in st.session_state.output_files
        if main_report_exists:
             utils.make_download_button(
                 st.session_state.output_files[config.STEP_RICH_TEXT_CHECK]["data"],
                 st.session_state.output_files[config.STEP_RICH_TEXT_CHECK]["file_name"],
                "下载富文本检查完整报告"
            )
        else: # Main report is missing
             st.warning("富文本检查报告丢失。")

        # Separately display download for non-compliant report if it exists
        if f"{config.STEP_RICH_TEXT_CHECK}_non_compliant" in st.session_state.output_files:
                non_compliant_file_info = st.session_state.output_files[f"{config.STEP_RICH_TEXT_CHECK}_non_compliant"]
                utils.make_download_button(
                    non_compliant_file_info["data"],
                    non_compliant_file_info["file_name"],
                    "下载富文本不合规条目报告"
                )
             
        col1_rt, col2_rt, col3_rt = st.columns(3)
        with col1_rt:
            if st.button("重新开始富文本检查", key="restart_rich_text_completed", type="secondary"):
                st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "pending"
                st.session_state.output_files.pop(config.STEP_RICH_TEXT_CHECK, None)
                st.session_state.output_files.pop(f"{config.STEP_RICH_TEXT_CHECK}_non_compliant", None) # Also clear non-compliant
                st.session_state.current_step_index = config.WORKFLOW_STEPS.index(config.STEP_RICH_TEXT_CHECK)
                st.session_state.rich_text_check_triggered = False
                st.session_state.rich_text_input_json_str = None
                st.session_state.rich_text_manual_upload_key += 1
                st.rerun()
        with col3_rt:
            if st.button(f"➡️ 进入下一步：{config.STEP_TRANSLATION_VALIDATION.split('. ')[1]}", key="proceed_from_rich_text", type="primary"):
                utils.complete_step_and_proceed(config.STEP_RICH_TEXT_CHECK, config.WORKFLOW_STEPS)

    elif st.session_state.step_status.get(config.STEP_RICH_TEXT_CHECK) == "failed":
        st.error("富文本检查失败或未产生有效结果。")
        col1_rtf, col2_rtf, col3_rtf = st.columns(3)
        with col1_rtf:
            if st.button("重试富文本检查", key="retry_rich_text_failed", type="secondary"):
                st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "pending"
                st.session_state.rich_text_check_triggered = False
                st.session_state.rich_text_input_json_str = None
                st.session_state.rich_text_manual_upload_key += 1
                st.rerun()
        with col3_rtf:
            if st.button(f"➡️ 跳过并进入下一步", key="skip_rich_text_failed", type="primary"):
                st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "completed"
                utils.complete_step_and_proceed(config.STEP_RICH_TEXT_CHECK, config.WORKFLOW_STEPS)

    else: 
        st.session_state.step_status[config.STEP_RICH_TEXT_CHECK] = "active"
        input_source_defined = False 

        prev_output_info = st.session_state.output_files.get(config.STEP_PREPROCESS)
        if prev_output_info and prev_output_info.get("type") == "json":
            st.info(f"检测到上一步骤 '{config.STEP_PREPROCESS}' 的输出，将使用其作为输入。")
            st.session_state.rich_text_input_json_str = prev_output_info["data"]
            input_source_defined = True
        
        if not input_source_defined:
            st.info("未检测到上一步的有效输出，请手动上传富文本检查所需的JSON文件。")
            uploaded_json_file = st.file_uploader(
                "上传预处理后的JSON文件", 
                type=["json"], 
                key=f"rich_text_manual_uploader_{st.session_state.rich_text_manual_upload_key}" 
            )
            if uploaded_json_file:
                try:
                    json_string = uploaded_json_file.getvalue().decode("utf-8")
                    parsed_json = json.loads(json_string) 
                    # --- 增强的JSON结构校验 ---
                    valid_json_structure = True
                    if not isinstance(parsed_json, dict):
                        st.error("JSON顶层结构必须是一个字典 (key-value 对集合)。")
                        valid_json_structure = False
                    else:
                        if not parsed_json: # 空字典
                            st.warning("上传的JSON文件是一个空字典，没有可处理的数据。")
                            # 允许空字典通过，但后续处理可能无事可做
                        else:
                            first_item_value = next(iter(parsed_json.values()))
                            if not isinstance(first_item_value, dict):
                                st.error("JSON中的条目数据必须是字典结构。")
                                valid_json_structure = False
                            else:
                                original_key = st.session_state.get('rich_text_original_key', "原文")
                                translated_key = st.session_state.get('rich_text_translated_key', "译文")
                                if not (original_key in first_item_value and translated_key in first_item_value):
                                    st.error(f"JSON条目中缺少必要的键：{original_key} 或 {translated_key}。请检查JSON内容或原文/译文Key配置。")
                                    valid_json_structure = False
                    
                    if valid_json_structure:
                        st.session_state.rich_text_input_json_str = json_string
                        input_source_defined = True
                        st.success(f"已加载并初步校验文件: {uploaded_json_file.name}")
                    else:
                        st.session_state.rich_text_input_json_str = None
                        input_source_defined = False
                    # --- 结束 JSON结构校验 ---
                except json.JSONDecodeError:
                    st.error("上传的文件不是有效的JSON格式。")
                    st.session_state.rich_text_input_json_str = None
                    input_source_defined = False
                except Exception as e:
                    st.error(f"读取上传的JSON文件失败: {e}")
                    st.session_state.rich_text_input_json_str = None
                    input_source_defined = False
        
        st.session_state.rich_text_original_key = st.text_input(
            "JSON中原文对应的Key:",
            value=st.session_state.get('rich_text_original_key', "原文")
        )
        st.session_state.rich_text_translated_key = st.text_input(
            "JSON中译文对应的Key:",
            value=st.session_state.get('rich_text_translated_key', "译文")
        )
        output_excel_name_rt = st.text_input("输出富文本检查报告文件名:", value=st.session_state.rich_text_output_excel_name)
        st.session_state.rich_text_output_excel_name = output_excel_name_rt

        start_button_disabled = not input_source_defined
        col1_start_rt, col2_start_rt, col3_start_rt = st.columns(3)
        with col3_start_rt:
            if st.button("开始富文本检查", key="start_rich_text_check", disabled=start_button_disabled, type="primary"):
                st.session_state.rich_text_check_triggered = True
                st.rerun()
        if start_button_disabled and not prev_output_info: 
             st.warning("请先上传有效的JSON文件。")

# ------------------------------
# 步骤3: AI翻译校验
# ------------------------------
elif active_step_name == config.STEP_TRANSLATION_VALIDATION:
    st.header(f":mag: {config.STEP_TRANSLATION_VALIDATION}")

    # --- 后台日志显示 --- (移到此处，确保始终可见) # MOVED INSIDE run_validation_batch for better refresh
    # st.markdown("--- 后台日志 ---")
    # log_output_area = st.text_area("实时日志输出:", value=st.session_state.get("validator_live_log", ""), height=200, key="validator_log_display_persistent", help="这里会显示处理过程中的详细信息和潜在错误。日志会自动追加，可手动清除。")
    # if st.button("清除日志显示", key="clear_validator_log_button_persistent"):
    #     st.session_state.validator_live_log = ""
    #     st.rerun()
    # --- 结束 后台日志显示 ---

    api_clients_val = None
    try:
        api_clients_val = {
            'qwen': OpenAI(api_key=st.session_state.api_keys["qwen"], base_url=st.session_state["qwen_api_url"]),
            'deepseek': OpenAI(api_key=st.session_state.api_keys["deepseek"], base_url=st.session_state["deepseek_api_url"])
        }
    except Exception as api_init_error:
        st.error(f"初始化API客户端失败: {api_init_error}")
        st.session_state.validator_triggered = False
        st.session_state.validator_processing_active = False
        st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "failed"

    if st.session_state.validator_triggered and api_clients_val and not st.session_state.validator_processing_active:
        if not st.session_state.get('validator_sample_ready', False) and \
           not st.session_state.get('validator_user_confirmed', False) and \
           not st.session_state.get('validator_should_stop', False):
            
            tasks_for_sample = st.session_state.validator_tasks_all[:config.SAMPLE_SIZE_VALIDATOR]
            if tasks_for_sample:
                st.session_state.validator_processing_active = True
                st.session_state.validator_batch_type = 'sample'
                st.rerun() 
            else:
                st.warning("没有找到用于生成样本的任务。可能是输入数据为空。")
                st.session_state.validator_triggered = False 
                st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "completed" 
                st.rerun()
        elif st.session_state.get('validator_user_confirmed', False) and \
             not st.session_state.get('validator_should_stop', False):
            tasks_for_full = st.session_state.validator_tasks_remaining
            if tasks_for_full:
                st.session_state.validator_processing_active = True
                st.session_state.validator_batch_type = 'full'
                st.rerun() 
            else: # No tasks remaining after sample, or no tasks at all if sample size covered all
                st.success("所有校验任务已完成 (可能仅处理了样本或无剩余任务)。")
                st.session_state.validator_triggered = False
                st.session_state.validator_processing_active = False # Ensure this is reset
                st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "completed"
                
                result_rows_final_formatted = []
                for key_val_full, data_val_full in st.session_state.validator_aggregated_results.items():
                     formatted_row = translation_validator._prepare_excel_row_data_validator(key_val_full, data_val_full)
                     result_rows_final_formatted.append({
                         "唯一索引": formatted_row.get("Key", key_val_full),
                         "原文": data_val_full.get("original_text", ""), 
                         "译文": data_val_full.get("translated_text", ""), 
                         "翻译质量分析": formatted_row.get("Analysis", ""), 
                         "中英直译": formatted_row.get("LiteralTranslation", ""),
                         "翻译拆解": formatted_row.get("Decomposition", "")
                     })
                 
                final_df_columns = ["唯一索引", "原文", "译文", "翻译质量分析", "中英直译", "翻译拆解"]
                final_df = pd.DataFrame(result_rows_final_formatted, columns=final_df_columns)
                
                st.session_state.output_files[config.STEP_TRANSLATION_VALIDATION] = {
                    "file_name": st.session_state.validator_output_excel_name,
                   "data": final_df, # This is a DataFrame
                   "type": "excel_df"  # Mark as DataFrame
                }
            
            # Reset processing flags after batch finishes or stops
            st.session_state.validator_processing_active = False
            st.session_state.validator_batch_type = None
            st.session_state.validator_triggered = False # Reset trigger after processing attempt
            st.rerun() # Rerun to show sample UI or final results / next step button
        else: # This case might be hit if user stops or other logic paths
             st.session_state.validator_triggered = False # Reset trigger

    # ----------------------------------------------------------------------
    # START OF THE CODE BLOCK TO BE RESTORED / ENSURED
    # ----------------------------------------------------------------------
    if st.session_state.validator_processing_active:
        batch_type = st.session_state.validator_batch_type
        tasks_to_process = []
        if batch_type == 'sample':
            tasks_to_process = st.session_state.validator_tasks_all[:config.SAMPLE_SIZE_VALIDATOR]
        elif batch_type == 'full':
            tasks_to_process = st.session_state.validator_tasks_remaining
        
        if tasks_to_process and api_clients_val:
            with st.spinner(f"AI翻译校验处理中 (批次: {batch_type})..."):
                processed_keys, batch_errors_from_run = run_validation_batch(api_clients_val, tasks_to_process, st.session_state["qwen_model_name"], st.session_state["deepseek_model_name"])
                st.session_state.validator_last_batch_errors = batch_errors_from_run 

            if st.session_state.get('validator_should_stop', False):
                st.warning("翻译校验已手动停止。")
                st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "failed" 
            elif batch_type == 'sample':
                 sample_data_list = [
                     translation_validator._prepare_excel_row_data_validator(k_sample, st.session_state.validator_aggregated_results[k_sample])
                     for k_sample in processed_keys 
                     if k_sample in st.session_state.validator_aggregated_results
                 ]
                 if sample_data_list:
                     st.session_state.validator_sample_df_for_display = pd.DataFrame(sample_data_list)
                     st.session_state.validator_sample_ready = True
                 else:
                      st.warning("样本批次处理完成，但未能生成预览数据。")
                      st.session_state.validator_sample_ready = False 
                      # If no sample data, perhaps auto-confirm to proceed or handle as error?
                      # For now, let's assume if sample processing yielded nothing, we might need to let user proceed or stop.
                      # Consider setting user_confirmed = True if no sample data is a valid scenario to skip preview.
            elif batch_type == 'full':
                 st.success("所有AI翻译校验任务已完成！")
                 st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "completed"
                 
                 result_rows_final_formatted = []
                 for key_val_full, data_val_full in st.session_state.validator_aggregated_results.items():
                      formatted_row = translation_validator._prepare_excel_row_data_validator(key_val_full, data_val_full)
                      result_rows_final_formatted.append({
                          "唯一索引": formatted_row.get("Key", key_val_full),
                          "原文": data_val_full.get("original_text", ""), 
                          "译文": data_val_full.get("translated_text", ""), 
                          "翻译质量分析": formatted_row.get("Analysis", ""), 
                          "中英直译": formatted_row.get("LiteralTranslation", ""),
                          "翻译拆解": formatted_row.get("Decomposition", "")
                      })
                 
                 final_df_columns = ["唯一索引", "原文", "译文", "翻译质量分析", "中英直译", "翻译拆解"]
                 final_df = pd.DataFrame(result_rows_final_formatted, columns=final_df_columns)
                 
                 st.session_state.output_files[config.STEP_TRANSLATION_VALIDATION] = {
                    "file_name": st.session_state.validator_output_excel_name,
                    "data": final_df, # This is a DataFrame
                    "type": "excel_df"  # Mark as DataFrame
                 }
            
            # Reset processing flags after batch finishes or stops
            st.session_state.validator_processing_active = False
            st.session_state.validator_batch_type = None
            st.session_state.validator_triggered = False # Reset trigger after processing attempt
            st.rerun() # Rerun to show sample UI or final results / next step button
        else:
             # This else is for: if not tasks_to_process or not api_clients_val
             st.error("无法开始批处理：缺少任务或API客户端初始化失败。")
             st.session_state.validator_processing_active = False
             st.session_state.validator_batch_type = None
             st.session_state.validator_triggered = False
             st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "failed"
             st.rerun()
    # ----------------------------------------------------------------------
    # END OF THE CODE BLOCK TO BE RESTORED / ENSURED
    # ----------------------------------------------------------------------
             
    elif st.session_state.get('validator_sample_ready', False) and \
         not st.session_state.get('validator_user_confirmed', False) and \
         not st.session_state.get('validator_should_stop', False):
        st.subheader("📊 样本数据预览 (首批已处理)")
        
        # Display errors from the last sample batch, if any
        if st.session_state.get("validator_last_batch_errors"):
            st.markdown("--- 样本处理期间的错误 ---")
            for err in st.session_state.validator_last_batch_errors:
                st.warning(f"条目 {err.get('key', 'N/A')} (任务类型: {err.get('type', 'N/A')}): {err.get('error', '未知错误')}")
            # Clear after displaying for sample, or they might show again if user stops and restarts sample
            # st.session_state.validator_last_batch_errors = [] 
            # Decided against clearing here, as it might be useful to see them persist if user stops/restarts sample phase without full run.

        if st.session_state.get('validator_sample_df_for_display') is not None:
            st.success("✅ 样本数据处理成功，请确认样本质量后继续")
            # Display relevant columns from the sample DataFrame
            display_columns = ["Key", "Original", "Translated", "Analysis", "LiteralTranslation", "Decomposition"]
            # Filter available columns from the DataFrame to avoid errors if some are missing
            available_display_columns = [col for col in display_columns if col in st.session_state.validator_sample_df_for_display.columns]
            st.dataframe(st.session_state.validator_sample_df_for_display[available_display_columns])

        else:
            st.info("正在生成样本数据...")
            
        st.warning("⚠️ 重要提示：大批量处理可能需要较长时间，处理期间请勿刷新页面或关闭浏览器，否则进度可能丢失。处理过程中会自动保存进度。")

        col1_val_confirm, col2_val_confirm = st.columns(2)
        with col1_val_confirm:
            if st.button("✅ 继续处理全部剩余条目", key="validator_confirm_sample_main_ui"):
                st.session_state.validator_user_confirmed = True
                st.session_state.validator_sample_ready = False
                st.session_state.validator_triggered = True
                st.rerun()
        with col2_val_confirm:
            if st.button("🛑 停止处理 (翻译校验)", key="validator_stop_sample_main_ui"):
                st.session_state.validator_should_stop = True
                st.session_state.validator_sample_ready = False
                st.session_state.validator_triggered = False
                st.session_state.validator_processing_active = False
                st.warning("翻译校验已停止。")
                st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "failed"
                st.rerun()

    elif st.session_state.step_status.get(config.STEP_TRANSLATION_VALIDATION) == "completed":
        st.info("AI翻译校验已完成。您可以下载报告或进入下一步。")
        
        # Display errors from the last full batch, if any
        if st.session_state.get("validator_last_batch_errors"):
            st.markdown("--- 处理期间发生的错误摘要 ---")
            with st.expander("点击查看详细错误列表", expanded=False):
                for err_idx, err in enumerate(st.session_state.validator_last_batch_errors):
                    st.error(f"错误 {err_idx + 1}: 条目 {err.get('key', 'N/A')} (任务类型: {err.get('type', 'N/A')}) - {err.get('error', '未知错误')}")
                    if err.get("details") != "TimeoutError": # Don't show full traceback for simple timeouts
                        st.code(err.get("details", "无详细信息"), language="text")
            st.session_state.validator_last_batch_errors = [] # Clear after displaying

        output_info = st.session_state.output_files.get(config.STEP_TRANSLATION_VALIDATION)
        if output_info:
            if output_info.get("type") == "excel_df" and isinstance(output_info.get("data"), pd.DataFrame):
                st.subheader("📊 AI校验结果预览 (部分数据)")
                df_to_display = output_info["data"]
                # 选择要展示的关键列，并确保它们存在
                preview_columns = ["唯一索引", "原文", "译文", "翻译质量分析", "中英直译", "翻译拆解"]
                available_columns = [col for col in preview_columns if col in df_to_display.columns]
                if not available_columns: # 如果核心列都不在，可能DataFrame有问题
                    st.warning("无法生成结果预览，DataFrame中缺少预期的关键列。")
                else:
                    st.dataframe(df_to_display[available_columns].head(20)) # 显示前20条作为预览
            
            utils.make_download_button(
                 output_info["data"],
                 output_info["file_name"],
                 "下载AI校验报告"
             )
        else:
             st.warning("校验报告文件似乎丢失了。")
        col1_val, col2_val, col3_val = st.columns(3)
        with col1_val:
            if st.button("重新开始AI翻译校验", key="restart_validation_completed", type="secondary"):
                st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "pending"
                st.session_state.output_files.pop(config.STEP_TRANSLATION_VALIDATION, None)
                st.session_state.current_step_index = config.WORKFLOW_STEPS.index(config.STEP_TRANSLATION_VALIDATION)
                st.session_state.validator_triggered = False
                st.session_state.validator_processing_active = False
                st.session_state.validator_sample_ready = False
                st.session_state.validator_user_confirmed = False
                st.session_state.validator_should_stop = False
                st.session_state.validator_sample_df_for_display = None
                st.session_state.validator_progress = {"completed": 0, "total": 0}
                st.session_state.validator_progress_check = {"completed": 0, "total": 0}
                st.session_state.validator_progress_literal = {"completed": 0, "total": 0}
                st.session_state.validator_progress_decompose = {"completed": 0, "total": 0}
                st.session_state.validator_aggregated_results = defaultdict(lambda: {"original_text": None, "translated_text": None, "standard_analysis": None, "rhyme_analysis": None, "literal_translation": None, "decomposition_analysis": None})
                st.session_state.output_files.pop(config.STEP_TRANSLATION_VALIDATION, None)
                st.session_state.validator_progress["completed"] = 0
                st.session_state.validator_checkpoint_counter = 0 # Reset checkpoint
                st.session_state.validator_checkpoint_results = {}
                st.session_state.validator_triggered = True
                st.rerun()
        with col3_val:
            if st.button(f"➡️ 进入下一步：{config.STEP_TRANSLATION_SCORING.split('. ')[1]}", key="proceed_from_validation", type="primary"):
                utils.complete_step_and_proceed(config.STEP_TRANSLATION_VALIDATION, config.WORKFLOW_STEPS)

    elif st.session_state.step_status.get(config.STEP_TRANSLATION_VALIDATION) == "failed":
        st.error("AI翻译校验失败或未产生有效结果。")
        col1_valf, col2_valf, col3_valf = st.columns(3)
        with col1_valf:
            if st.button("重试AI翻译校验", key="retry_validation_failed", type="secondary"):
                st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "pending"
                st.session_state.validator_triggered = False
                st.session_state.validator_processing_active = False
                st.session_state.validator_sample_ready = False
                st.session_state.validator_user_confirmed = False
                st.session_state.validator_should_stop = False
                st.session_state.validator_sample_df_for_display = None
                st.session_state.validator_progress = {"completed": 0, "total": 0}
                st.session_state.validator_progress_check = {"completed": 0, "total": 0}
                st.session_state.validator_progress_literal = {"completed": 0, "total": 0}
                st.session_state.validator_progress_decompose = {"completed": 0, "total": 0}
                st.session_state.validator_aggregated_results = defaultdict(lambda: {"original_text": None, "translated_text": None, "standard_analysis": None, "rhyme_analysis": None, "literal_translation": None, "decomposition_analysis": None})
                st.session_state.output_files.pop(config.STEP_TRANSLATION_VALIDATION, None)
                st.session_state.validator_progress["completed"] = 0
                st.session_state.validator_checkpoint_counter = 0 # Reset checkpoint
                st.session_state.validator_checkpoint_results = {}
                st.session_state.validator_triggered = True
                st.rerun()
        with col3_valf:
            if st.button(f"➡️ 跳过并进入下一步", key="skip_validation_failed", type="primary"):
                st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "completed"
                utils.complete_step_and_proceed(config.STEP_TRANSLATION_VALIDATION, config.WORKFLOW_STEPS)

    else: 
        st.session_state.step_status[config.STEP_TRANSLATION_VALIDATION] = "active"
        input_source_defined = False

        prev_output_info_val = st.session_state.output_files.get(config.STEP_PREPROCESS) 
        if prev_output_info_val and prev_output_info_val.get("type") == "json":
            st.info(f"检测到上一步骤 '{config.STEP_PREPROCESS}' 的输出，将使用其作为输入。")
            st.session_state.validator_input_json_str = prev_output_info_val["data"]
            input_source_defined = True
        
        if not input_source_defined:
            st.info(f"未检测到上一步的有效输出，请手动上传AI校验所需的JSON文件。")
            uploaded_json_file_val = st.file_uploader(
                "上传预处理后的JSON文件 (同步骤1输出)", 
                type=["json"], 
                key=f"validator_manual_uploader_{st.session_state.validator_manual_upload_key}"
            )
            if uploaded_json_file_val:
                try:
                    json_string = uploaded_json_file_val.getvalue().decode("utf-8")
                    parsed_json = json.loads(json_string)
                    # --- 增强的JSON结构校验 (AI翻译校验步骤) ---
                    valid_json_structure = True
                    if not isinstance(parsed_json, dict):
                        st.error("JSON顶层结构必须是一个字典 (key-value 对集合)。")
                        valid_json_structure = False
                    else:
                        if not parsed_json: # 空字典
                            st.warning("上传的JSON文件是一个空字典，没有可处理的数据。")
                        else:
                            # 检查第一个条目的结构作为代表
                            first_item_key = next(iter(parsed_json.keys()))
                            first_item_value = parsed_json[first_item_key]
                            if not isinstance(first_item_value, dict):
                                st.error("JSON中的条目数据必须是字典结构。")
                                valid_json_structure = False
                            else:
                                # 核心字段检查 (至少应有原文和译文)
                                required_keys = ["原文", "译文"]
                                missing_keys = [k for k in required_keys if k not in first_item_value]
                                if missing_keys:
                                    st.error(f"JSON条目 (例如条目 '{first_item_key}') 中缺少核心字段：{', '.join(missing_keys)}。")
                                    valid_json_structure = False
                    
                    if valid_json_structure:
                        st.session_state.validator_input_json_str = json_string
                        input_source_defined = True
                        st.success(f"已加载并初步校验文件: {uploaded_json_file_val.name}")
                        # 清除旧的任务和检查点，因为输入源已改变
                        reset_validator_state_for_fresh_start()
                        st.session_state.validator_input_json_str = json_string # 重新赋值，因为reset会清除它
                    else:
                        st.session_state.validator_input_json_str = None
                        input_source_defined = False
                    # --- 结束 JSON结构校验 ---
                except json.JSONDecodeError:
                    st.error("上传的文件不是有效的JSON格式。")
                    st.session_state.validator_input_json_str = None
                    input_source_defined = False
                except Exception as e:
                    st.error(f"读取上传的JSON文件失败: {e}")
                    st.session_state.validator_input_json_str = None
                    input_source_defined = False

        output_excel_name_val = st.text_input("输出AI校验报告文件名:", value=st.session_state.validator_output_excel_name)
        st.session_state.validator_output_excel_name = output_excel_name_val

        st.markdown("##### 集成处理选项:")
        col_toggle1, col_toggle2 = st.columns(2)
        with col_toggle1:
            st.session_state.validator_enable_literal_translation = st.checkbox(
                "同时进行中英直译", 
                value=st.session_state.get('validator_enable_literal_translation', True),
                key="val_toggle_literal_trans"
            )
        with col_toggle2:
            st.session_state.validator_enable_decomposition = st.checkbox(
                "同时进行翻译拆解", 
                value=st.session_state.get('validator_enable_decomposition', True),
                key="val_toggle_decomposition"
            )

        if input_source_defined:
            # Prepare tasks only if they are not already prepared for the current input source
            # or if critical settings that affect task generation have changed.
            # For simplicity, we re-prepare if tasks_all is empty, assuming validator_input_json_str is the key driver.
            if not st.session_state.validator_tasks_all: 
                try: 
                    source_data = json.loads(st.session_state.validator_input_json_str)
                    tasks_to_submit_all = []
                    for key, item in source_data.items():
                        tb_str = str(item.get("翻译背景信息", ""))
                        if isinstance(item.get("翻译背景信息"), dict):
                            try: tb_str = json.dumps(item.get("翻译背景信息"), ensure_ascii=False, indent=2)
                            except TypeError: pass
                        prompt_args = {
                            "category": item.get("分类", ""), 
                            "original_text": item.get("原文", ""), 
                            "translated_text": item.get("译文", ""), 
                            "character_info": item.get("角色相关信息", ""), 
                            "translation_background": tb_str
                        }
                        tasks_to_submit_all.append({'key': key, 'item': item, 'prompt': config.STANDARD_PROMPT_TEXT_VALIDATOR.format(**prompt_args), 'type': 'standard'})
                        if str(item.get("押韵", "0")) == "1":
                            rhyme_prompt = config.RHYMING_PROMPT_TEXT_VALIDATOR.format(**prompt_args)
                            tasks_to_submit_all.append({'key': key, 'item': item, 'prompt': rhyme_prompt, 'type': 'rhyme'})
                    
                    st.session_state.validator_tasks_all = tasks_to_submit_all
                    sample_item_keys = list(dict.fromkeys(t['key'] for t in tasks_to_submit_all))[:config.SAMPLE_SIZE_VALIDATOR]
                    sample_task_count = sum(1 for t in tasks_to_submit_all if t['key'] in sample_item_keys)
                    st.session_state.validator_tasks_remaining = tasks_to_submit_all[sample_task_count:]
                    
                    num_unique_items = len(source_data) 
                    st.session_state.validator_progress_check["total"] = num_unique_items
                    st.session_state.validator_progress_check["completed"] = 0
                    
                    st.session_state.validator_progress_literal["total"] = num_unique_items if st.session_state.get('validator_enable_literal_translation', False) else 0
                    st.session_state.validator_progress_literal["completed"] = 0

                    st.session_state.validator_progress_decompose["total"] = num_unique_items if st.session_state.get('validator_enable_decomposition', False) else 0
                    st.session_state.validator_progress_decompose["completed"] = 0
                    
                    st.session_state.validator_progress["total"] = len(tasks_to_submit_all) 
                    st.session_state.validator_progress["completed"] = 0
                            
                except json.JSONDecodeError as json_e:
                    st.error(f"无法解析输入JSON数据 (准备任务时): {json_e}")
                    st.session_state.validator_tasks_all = []
                    st.session_state.validator_tasks_remaining = []
                    input_source_defined = False
                except Exception as e:
                    st.error(f"准备任务时出错: {e}")
                    st.session_state.validator_tasks_all = []
                    st.session_state.validator_tasks_remaining = []
                    input_source_defined = False
            
            # If input_source_defined is false at this point (either initially or after a failed load/prep),
            # ensure tasks are cleared.
            if not input_source_defined:
                st.session_state.validator_tasks_all = []
                st.session_state.validator_tasks_remaining = []

            # --- Resume Logic Check (Now with File Checkpoint) ---
            if input_source_defined and st.session_state.validator_input_json_str and \
               not st.session_state.get('validator_should_resume_processing', False) and \
               not st.session_state.validator_processing_active and \
               not st.session_state.validator_triggered:
                
                loaded_checkpoint_data = load_checkpoint_from_file(st.session_state.validator_input_json_str)
                
                if loaded_checkpoint_data and loaded_checkpoint_data.get('validator_checkpoint_results'):
                    st.info("检测到与此文件匹配的之前未完成的校验任务 (来自本地检查点)。您可以选择继续或放弃。")
                    col_resume1, col_resume2 = st.columns(2)
                    with col_resume1:
                        if st.button("🔄 继续上次进度 (从文件恢复)", key="validator_resume_from_file_button"):
                            # Populate session state from loaded_checkpoint_data
                            st.session_state.validator_checkpoint_results = loaded_checkpoint_data.get('validator_checkpoint_results', {})
                            st.session_state.validator_tasks_all = loaded_checkpoint_data.get('validator_tasks_all', [])
                            st.session_state.validator_progress = loaded_checkpoint_data.get('validator_progress', {"completed": 0, "total": 0})
                            st.session_state.validator_progress_check = loaded_checkpoint_data.get('validator_progress_check', {"completed": 0, "total": 0})
                            st.session_state.validator_progress_literal = loaded_checkpoint_data.get('validator_progress_literal', {"completed": 0, "total": 0})
                            st.session_state.validator_progress_decompose = loaded_checkpoint_data.get('validator_progress_decompose', {"completed": 0, "total": 0})
                            # Convert lists back to sets for _completed items
                            st.session_state.validator_items_check_completed = set(loaded_checkpoint_data.get('validator_items_check_completed', []))
                            st.session_state.validator_items_literal_completed = set(loaded_checkpoint_data.get('validator_items_literal_completed', []))
                            st.session_state.validator_items_decompose_completed = set(loaded_checkpoint_data.get('validator_items_decompose_completed', []))
                            
                            st.session_state.validator_input_json_str_for_resume = st.session_state.validator_input_json_str # Store for repopulate
                            
                            repopulate_validator_state_from_checkpoint() # This populates aggregated_results and progress based on st.session_state
                            
                            # Correctly set tasks_remaining based on ALL tasks and what's in checkpoint_results
                            # repopulate_validator_state_from_checkpoint already sets progress["completed"] for main tasks
                            # We need to determine tasks_remaining based on what was *not* completed for validation prompts
                            completed_prompt_keys_types = set()
                            for task_key, task_data in st.session_state.validator_checkpoint_results.items():
                                if task_data.get('standard_analysis_parsed') is not None:
                                    completed_prompt_keys_types.add((task_key, 'standard'))
                                if task_data.get('rhyme_analysis_parsed') is not None:
                                    completed_prompt_keys_types.add((task_key, 'rhyme'))
                            
                            all_prompts_tasks = st.session_state.validator_tasks_all
                            remaining_prompts_tasks = [
                                task for task in all_prompts_tasks 
                                if (task['key'], task['type']) not in completed_prompt_keys_types
                            ]
                            st.session_state.validator_tasks_remaining = remaining_prompts_tasks
                            # Ensure total for main progress is based on all tasks loaded from checkpoint if available
                            if st.session_state.validator_tasks_all:
                                st.session_state.validator_progress["total"] = len(st.session_state.validator_tasks_all)

                            st.session_state.validator_should_resume_processing = True
                            st.session_state.validator_triggered = True 
                            st.session_state.validator_sample_ready = False 
                            st.session_state.validator_user_confirmed = True 
                            st.success("已从本地检查点恢复状态，即将继续处理...")
                            st.rerun()
                    with col_resume2:
                        if st.button("🗑️ 放弃并重新开始 (删除文件检查点)", key="validator_discard_resume_file_button"):
                            if st.session_state.get("validator_input_json_str"):
                                delete_checkpoint_file(st.session_state.validator_input_json_str)
                            reset_validator_state_for_fresh_start() 
                            st.rerun() 
            # --- End Resume Logic Check ---

        start_button_disabled_val = not (input_source_defined and st.session_state.validator_tasks_all)
        col1_start_val, col2_start_val, col3_start_val = st.columns(3)
        with col3_start_val:
            if st.button("开始AI翻译校验", key="start_validation", disabled=start_button_disabled_val, type="primary"):
                if not api_clients_val: 
                    st.error("API客户端未初始化，无法开始。")
                else:
                    if not st.session_state.get('validator_should_resume_processing', False):
                        # This is a fresh start, not a resume, so reset relevant states
                        st.session_state.validator_sample_ready = False
                        st.session_state.validator_user_confirmed = False
                        st.session_state.validator_should_stop = False
                        st.session_state.validator_sample_df_for_display = None
                        st.session_state.validator_aggregated_results = defaultdict(lambda: {"original_text": None, "translated_text": None, "standard_analysis": None, "rhyme_analysis": None, "literal_translation": None, "decomposition_analysis": None})
                        st.session_state.output_files.pop(config.STEP_TRANSLATION_VALIDATION, None)
                        st.session_state.validator_progress["completed"] = 0
                        st.session_state.validator_checkpoint_counter = 0 
                        st.session_state.validator_checkpoint_results = {}
                        st.session_state.validator_items_check_completed = set()
                        st.session_state.validator_items_literal_completed = set()
                        st.session_state.validator_items_decompose_completed = set()
                        st.session_state.validator_live_log = "" # Clear live log for fresh start
                        # Task totals (validator_progress['total'], etc.) are set during task preparation, so no need to reset here if tasks_all is repopulated.
                    else:
                        # This is a resume, states should have been set by repopulate_validator_state_from_checkpoint
                        # Ensure sample stage is correctly bypassed if resuming
                        st.session_state.validator_sample_ready = False
                        st.session_state.validator_user_confirmed = True 

                    st.session_state.validator_triggered = True
                    st.rerun()

# ------------------------------
# 步骤4: AI翻译评分
# ------------------------------
elif active_step_name == config.STEP_TRANSLATION_SCORING:
    st.header(f":trophy: {config.STEP_TRANSLATION_SCORING}")

    if st.session_state.scoring_triggered:
        input_df_bytes = st.session_state.get('scoring_input_df_bytes')
        if input_df_bytes:
            with st.spinner("正在进行AI翻译评分..."):
                try:
                    input_stream = io.BytesIO(input_df_bytes)
                    input_df_for_scoring = pd.read_excel(input_stream)
                    scored_df = translation_scorer.run_translation_scoring_core(input_df_for_scoring)

                    output_excel_name = st.session_state.scoring_output_excel_name
                    if not scored_df.empty:
                        output_buffer = io.BytesIO()
                        with pd.ExcelWriter(output_buffer, engine='openpyxl') as writer:
                            scored_df.to_excel(writer, index=False)
                        scored_bytes = output_buffer.getvalue()
                        st.session_state.output_files[config.STEP_TRANSLATION_SCORING] = {
                            "file_name": output_excel_name,
                            "data": scored_bytes, 
                            "type": "excel_bytes"
                        }
                        st.session_state.step_status[config.STEP_TRANSLATION_SCORING] = "completed"
                        st.success("AI翻译评分完成！工作流程已全部完成。")
                        st.session_state.current_step_index = len(config.WORKFLOW_STEPS) # Move to end
                    else:
                        st.session_state.step_status[config.STEP_TRANSLATION_SCORING] = "failed"
                        st.error("AI翻译评分未产生结果或输入为空。")

                except Exception as e:
                    st.session_state.step_status[config.STEP_TRANSLATION_SCORING] = "failed"
                    st.error(f"AI翻译评分处理失败: {e}")

            st.session_state.scoring_triggered = False
            st.session_state.scoring_input_df_bytes = None
            st.session_state.scoring_manual_upload_key += 1 
            st.rerun()
        else:
            st.error("AI翻译评分输入数据丢失，请返回上一步或重新上传。")
            st.session_state.scoring_triggered = False
            st.session_state.step_status[config.STEP_TRANSLATION_SCORING] = "failed"
            st.session_state.scoring_manual_upload_key += 1
            st.rerun()

    elif st.session_state.step_status.get(config.STEP_TRANSLATION_SCORING) == "completed":
        st.info("AI翻译评分已完成。这是工作流的最后一步。")
        if config.STEP_TRANSLATION_SCORING in st.session_state.output_files:
            utils.make_download_button(
                st.session_state.output_files[config.STEP_TRANSLATION_SCORING]["data"],
                st.session_state.output_files[config.STEP_TRANSLATION_SCORING]["file_name"],
                "下载AI评分报告"
            )
        else:
             st.warning("评分报告文件似乎丢失了。")

        if st.button("重新运行AI翻译评分", key="restart_scoring_completed"):
            st.session_state.step_status[config.STEP_TRANSLATION_SCORING] = "pending"
            st.session_state.output_files.pop(config.STEP_TRANSLATION_SCORING, None)
            st.session_state.current_step_index = config.WORKFLOW_STEPS.index(config.STEP_TRANSLATION_SCORING)
            st.session_state.scoring_triggered = False
            st.session_state.scoring_input_df_bytes = None
            st.session_state.scoring_manual_upload_key += 1
            st.rerun()

    elif st.session_state.step_status.get(config.STEP_TRANSLATION_SCORING) == "failed":
        st.error("AI翻译评分失败或未产生有效结果。")
        col1_score_f, col2_score_f, col3_score_f = st.columns(3)
        with col1_score_f:
            if st.button("重试AI翻译评分", key="retry_scoring_failed", type="secondary"):
                st.session_state.step_status[config.STEP_TRANSLATION_SCORING] = "pending"
                st.session_state.scoring_triggered = False
                st.session_state.scoring_input_df_bytes = None
                st.session_state.scoring_manual_upload_key += 1
                st.rerun()
        with col3_score_f:
            if st.button("➡️ 完成工作流", key="complete_workflow_with_failed_scoring", type="primary"):
                st.session_state.current_step_index = len(config.WORKFLOW_STEPS) # Move to end
                st.rerun()

    else: 
        st.session_state.step_status[config.STEP_TRANSLATION_SCORING] = "active"
        input_source_defined = False

        prev_output_info_score = st.session_state.output_files.get(config.STEP_TRANSLATION_VALIDATION)
        if prev_output_info_score and prev_output_info_score.get("type") == "excel_bytes": 
            st.info(f"检测到上一步骤 '{config.STEP_TRANSLATION_VALIDATION}' 的输出 (旧版字节流)，将使用其作为输入。")
            st.session_state.scoring_input_df_bytes = prev_output_info_score["data"]
            input_source_defined = True
        elif prev_output_info_score and prev_output_info_score.get("type") == "excel_df":
            st.info(f"检测到上一步骤 '{config.STEP_TRANSLATION_VALIDATION}' 的输出 (DataFrame)，将使用其作为输入。")
            input_df_from_prev = prev_output_info_score["data"]
            if isinstance(input_df_from_prev, pd.DataFrame):
                try:
                    output_buffer = io.BytesIO()
                    with pd.ExcelWriter(output_buffer, engine='openpyxl') as writer:
                        input_df_from_prev.to_excel(writer, index=False)
                    st.session_state.scoring_input_df_bytes = output_buffer.getvalue()
                    input_source_defined = True
                except Exception as convert_e:
                     st.error(f"将步骤3的DataFrame转换为Excel字节时出错: {convert_e}")
                     st.session_state.scoring_input_df_bytes = None
                     input_source_defined = False
            else:
                st.warning(f"步骤3的输出数据类型不是预期的DataFrame，而是 {type(input_df_from_prev).__name__}。无法自动使用。")
                st.session_state.scoring_input_df_bytes = None
                input_source_defined = False
        
        if not input_source_defined:
            st.info(f"未检测到上一步的有效输出，请手动上传AI评分所需的Excel文件 (AI校验报告)。")
            uploaded_excel_file_score = st.file_uploader(
                "上传AI校验报告 (.xlsx)", 
                type=["xlsx", "xls"],
                key=f"scoring_manual_uploader_{st.session_state.scoring_manual_upload_key}"
            )
            if uploaded_excel_file_score:
                try:
                    excel_bytes = uploaded_excel_file_score.getvalue()
                    pd.read_excel(io.BytesIO(excel_bytes)) # Validate
                    st.session_state.scoring_input_df_bytes = excel_bytes
                    input_source_defined = True
                    st.success(f"已加载文件: {uploaded_excel_file_score.name}")
                except Exception as e:
                    st.error(f"读取上传的Excel文件失败或文件无效: {e}")
                    st.session_state.scoring_input_df_bytes = None
                    input_source_defined = False

        default_output_name = "translation_scored_report.xlsx" 
        if prev_output_info_score and prev_output_info_score.get('file_name'):
            try:
                 base, ext = os.path.splitext(prev_output_info_score['file_name'])
                 default_output_name = f"{base}_scored{ext}"
            except: pass
        elif 'uploaded_excel_file_score' in locals() and uploaded_excel_file_score:
             try:
                 base, ext = os.path.splitext(uploaded_excel_file_score.name)
                 default_output_name = f"{base}_scored{ext}"
             except Exception: pass 
        
        output_excel_name_score = st.text_input("输出AI评分文件名:", value=st.session_state.get('scoring_output_excel_name', default_output_name))
        st.session_state.scoring_output_excel_name = output_excel_name_score

        start_button_disabled_score = not input_source_defined
        col1_start_score, col2_start_score, col3_start_score = st.columns(3)
        with col3_start_score:
            if st.button("开始AI翻译评分", key="start_scoring", disabled=start_button_disabled_score, type="primary"):
                 st.session_state.scoring_triggered = True
                 st.rerun()
        if start_button_disabled_score and not prev_output_info_score:
             st.warning("请先上传有效的Excel文件。")

# ------------------------------
# 流程结束
# ------------------------------
elif active_step_index >= len(config.WORKFLOW_STEPS) and \
     st.session_state.step_status.get(config.WORKFLOW_STEPS[-1]) == "completed":
    st.header("🎉 流程已全部完成！ 🎉")
    st.success("所有主要步骤均已成功处理。您可以下载各步骤的产出文件。")
    
    st.subheader("产出文件下载:")
    download_cols = st.columns(max(1, len(config.WORKFLOW_STEPS))) 
    col_idx = 0
    for step_name_completed in config.WORKFLOW_STEPS:
        if st.session_state.step_status.get(step_name_completed) == "completed" and step_name_completed in st.session_state.output_files:
            file_info = st.session_state.output_files[step_name_completed]
            with download_cols[col_idx % len(download_cols)]:
                st.markdown(f"**{step_name_completed.split('. ')[1]}:**")
                utils.make_download_button(file_info["data"], file_info["file_name"], f"下载 {step_name_completed.split('. ')[1]} 文件")
                st.markdown("&nbsp;") 
            col_idx += 1
        elif st.session_state.step_status.get(step_name_completed) == "failed" or st.session_state.step_status.get(step_name_completed) == "pending":
            with download_cols[col_idx % len(download_cols)]:
                st.markdown(f"**{step_name_completed.split('. ')[1]}:**")
                st.warning(f"{step_name_completed.split('. ')[1]} 未完成或跳过，无产出文件。")
                st.markdown("&nbsp;")
            col_idx +=1

    if col_idx == 0: 
        st.info("没有可供下载的产出文件。")

    st.markdown("---")
    if st.button("返回第一步重新开始整个工作流", key="restart_all_completed"):
        st.session_state.current_step_index = 0
        st.session_state.step_status = {step: "pending" for step in config.WORKFLOW_STEPS}
        st.session_state.output_files = {}
        # Reset all other relevant session state variables defined in init_session_state
        # This is a simplified reset; a more robust reset would re-call parts of init_session_state
        # or iterate through expected keys to reset them.
        # For now, this covers the main ones for a fresh start.
        for key in list(st.session_state.keys()):
            if key.startswith('preprocess_') or \
               key.startswith('rich_text_') or \
               key.startswith('validator_') or \
               key.startswith('scoring_') or \
               key == 'excel_columns' or \
               key == 'column_mappings':
                try:
                    del st.session_state[key]
                except KeyError:
                    pass # Key might have already been deleted or not set
        init_session_state() # Re-initialize to set defaults
        st.rerun()

# --- 页脚 ---
st.markdown("---")
st.caption("翻译自动化辅助处理平台 - 构建中")
