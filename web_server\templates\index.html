<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译自动化辅助处理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f7f6; color: #333; display: flex; flex-direction: column; min-height: 100vh; }
        .top-bar { background-color: #343a40; padding: 10px 20px; display: flex; justify-content: center; align-items: center; flex-wrap: wrap; }
        .mode-tabs button, .global-tabs button { background-color: #495057; color: white; border: none; padding: 10px 20px; margin: 5px; cursor: pointer; border-radius: 4px; font-size: 1em; transition: background-color 0.3s; }
        .mode-tabs button.active, .global-tabs button.active { background-color: #007bff; }
        .mode-tabs button:hover, .global-tabs button:hover { background-color: #6c757d; }
        
        .main-container { display: flex; flex-grow: 1; margin: 10px; }
        .content-area { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); width: 100%; }
        
        h1, h2, h3 { color: #2c3e50; }
        h1.page-title { text-align: center; margin: 20px 0 30px 0; font-size: 1.8em; color: #fff; background-color: #007bff; padding: 15px; border-radius: 5px;}
        h2.section-title { border-bottom: 2px solid #007bff; padding-bottom: 8px; margin-top: 25px; font-size: 1.4em; }

        .step-breadcrumb-pro { display: flex; justify-content: space-around; margin-bottom: 20px; padding: 10px 0; background-color: #e9ecef; border-radius: 5px; }
        .step-breadcrumb-pro .step-item {
            padding: 10px 15px; border: 1px solid transparent; background-color: #f8f9fa;
            color: #495057; font-size: 0.95em; cursor: pointer; border-radius: 4px; 
            transition: background-color 0.3s, color 0.3s, border-color 0.3s;
            text-align: center; flex-grow: 1; margin: 0 5px;
            border: 2px solid #dee2e6; /* Default border */
        }
        .step-breadcrumb-pro .step-item.pending { border-color: #dee2e6; background-color: #f8f9fa; color: #6c757d;}
        .step-breadcrumb-pro .step-item.running { border-color: #ffc107; background-color: #fff3cd; color: #856404; animation: pulse-yellow 2s infinite;}
        .step-breadcrumb-pro .step-item.completed { border-color: #28a745; background-color: #d4edda; color: #155724;}
        .step-breadcrumb-pro .step-item.failed { border-color: #dc3545; background-color: #f8d7da; color: #721c24;}
        .step-breadcrumb-pro .step-item.active-step { /* When this step's content is shown, even if pending */
             box-shadow: 0 0 0 0.2rem rgba(0,123,255,.5);
             font-weight: bold;
        }

        @keyframes pulse-yellow {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }

        .content-section { display: none; padding: 15px; border-top: 1px solid #eee; margin-top:10px;}
        .content-section.active { display: block; }
        
        label { display: block; margin-top: 12px; margin-bottom: 5px; font-weight: bold; font-size: 0.95em; }
        input[type="file"], select { margin-bottom: 15px; padding: 10px; border-radius: 4px; border: 1px solid #ddd; width: calc(100% - 22px); box-sizing: border-box; background-color: #fff; }
        input[type="checkbox"] { margin-right: 5px; vertical-align: middle;}
        label.inline-label { font-weight: normal; display: inline-block !important; margin-right: 15px;}

        button { background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 1em; transition: background-color 0.3s; margin-top: 15px; margin-right: 8px;}
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        .button-group { margin-top: 25px; text-align: right; border-top: 1px solid #eee; padding-top: 15px;}
        
        .hidden { display: none !important; }
        
        .status-block, .results-block, .config-block { margin-top: 20px; padding: 15px; border: 1px solid #eee; border-radius: 4px; background-color: #f9f9f9; }
        
        .error-msg { color: #dc3545; font-weight: bold; margin-top: 10px; }
        .success-msg { color: #28a745; font-weight: bold; margin-top: 10px; }
        .log-display-area { margin-top:10px; padding:15px; border:1px solid #444; background-color:#1e1e1e; color:#d4d4d4; font-family:'Courier New', monospace; white-space:pre-wrap; overflow-y:auto; font-size:12px; border-radius: 5px; }

        .log-controls { margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6; }
        .log-controls label { margin-right: 20px; font-weight: normal; }
        .log-controls input[type="radio"] { margin-right: 5px; }

        .button-group { margin: 10px 0; }
        .button-group button { margin-right: 10px; margin-bottom: 5px; }

        /* File choice and step output styles */
        .input-file-section { margin: 15px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6; }
        .file-choice-options { margin: 10px 0; }
        .file-choice-options label { display: block; margin: 8px 0; font-weight: normal; }
        .file-choice-options input[type="radio"] { margin-right: 8px; }
        .custom-upload-section { margin-top: 15px; padding: 10px; background-color: #fff; border-radius: 3px; border: 1px solid #ccc; }

        .step-output-actions { margin-top: 15px; display: flex; gap: 10px; align-items: center; flex-wrap: wrap; }
        .download-btn { background-color: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 14px; }
        .download-btn:hover { background-color: #218838; color: white; text-decoration: none; }
        .continue-btn { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .continue-btn:hover { background-color: #0056b3; }
        .continue-btn:disabled { background-color: #6c757d; cursor: not-allowed; }

        .workflow-complete { margin-top: 15px; padding: 15px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; text-align: center; }
        
        .mapping-row { display: flex; align-items: center; margin-bottom: 10px; padding: 8px; background-color: #f8f9fa; border-radius: 3px;}
        .mapping-row label { flex: 2; margin-right: 10px; margin-top: 0; font-weight: normal; }
        .mapping-row select { flex: 3; margin-bottom: 0; }
        .core-field-note { font-size: 0.9em; color: #666; margin-bottom: 15px;}
        .field-core-marker { color: red; font-weight: bold; margin-left: 3px;}

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border-left-color: #007bff;
            animation: spin 1s ease infinite;
            margin: 15px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Progress Bar for AI Validation */
        .progress-bar-container {
            width: 100%; 
            background-color: #e9ecef; 
            border-radius: .25rem; 
            margin: 10px 0;
        }
        .progress-bar {
            width: 0%; /* Initial width */
            height: 20px; 
            background-color: #007bff; 
            border-radius: .25rem; 
            text-align: center; 
            line-height: 20px; 
            color: white; 
            transition: width .6s ease;
            font-size: 0.85em;
        }

    </style>
</head>
<body>
    <h1 class="page-title">翻译自动化辅助处理系统</h1>
    <div class="top-bar">
        <div class="mode-tabs">
            <button id="simpleModeTab" class="active" onclick="switchMode('simple')">简单模式</button>
            <button id="professionalModeTab" onclick="switchMode('professional')">专业模式</button>
        </div>
        <div class="global-tabs">
            <button id="logTab" onclick="showGlobalTabContent('log_display_content')">后台日志</button>
            <!-- Add other global tabs here if needed -->
        </div>
    </div>

    <div class="main-container">
        <div class="content-area">
            <!-- Simple Mode Content -->
            <div id="simpleModeContent" class="content-section active">
                <h2 class="section-title">简单模式处理</h2>
                <div id="simple_upload_config_area">
                    <label for="simple_excelFile">1. 选择Excel文件:</label>
                    <input type="file" id="simple_excelFile" accept=".xlsx, .xls">
                    <div id="simple_uploadFileInfo" class="log-area hidden" style="background-color: #e9ecef; color: #495057;"></div>
                    <div id="simple_spinner_upload" class="spinner hidden"></div>

                    <div id="simple_columnMappingSectionContainer" class="config-block hidden">
                        <h3>2. 列映射配置 <span style="font-weight:normal; font-size:0.9em">(若不配置，将使用默认表头)</span></h3>
                        <div id="simple_columnMappingsDiv"></div>
                    </div>

                    <div id="simple_params_config_area" class="config-block hidden">
                        <h3>3. AI处理参数配置</h3>
                        <input type="checkbox" id="simple_enableLiteralTranslation" name="simple_enableLiteralTranslation">
                        <label for="simple_enableLiteralTranslation" class="inline-label">启用中英直译</label><br>
                        <input type="checkbox" id="simple_enableDecomposition" name="simple_enableDecomposition">
                        <label for="simple_enableDecomposition" class="inline-label">启用翻译拆解</label><br>
                        <!-- Simple mode scoring is usually not skipped unless a global param, or it runs by default -->
                    </div>
                    <div class="button-group">
                        <button id="simple_startProcessingBtn" disabled>开始处理 (简单模式)</button>
                    </div>
                </div>
                <div id="simple_status_results_area" class="status-block hidden">
                    <h3>处理状态与结果</h3>
                    <p><strong>任务ID:</strong> <span id="simple_taskIdDisplay"></span></p>
                    <p><strong>总体状态:</strong> <span id="simple_overallStatusDisplay"></span></p>
                    <div id="simple_ai_progress_container" class="hidden">
                        <p>AI校验进度:</p>
                        <div class="progress-bar-container">
                            <div id="simple_ai_progressBar" class="progress-bar">0%</div>
                        </div>
                    </div>
                    <p id="simple_errorMessage" class="error-msg hidden"></p>
                    <a id="simple_downloadLog" href="#" class="hidden" target="_blank">下载完整日志</a>
                    <div id="simple_output_files_list" class="results-block hidden">
                        <h4>输出文件:</h4>
                        <ul id="simple_outputFilesUl"></ul>
                    </div>
                </div>
            </div>

            <!-- Professional Mode Content -->
            <div id="professionalModeContent" class="content-section">
                <h2 class="section-title">专业模式处理</h2>
                <div class="step-breadcrumb-pro" id="pro_step_indicators">
                    <div class="step-item pending" id="pro_step_preprocess" onclick="activateProStep('preprocess')">数据预处理</div>
                    <div class="step-item pending" id="pro_step_richtext" onclick="activateProStep('richtext')">富文本校验</div>
                    <div class="step-item pending" id="pro_step_ai_processing" onclick="activateProStep('ai_processing')">AI校验</div>
                    <div class="step-item pending" id="pro_step_scoring" onclick="activateProStep('scoring')">翻译评分</div>
                </div>

                <div id="pro_task_info_overall" class="status-block hidden">
                    <p><strong>当前任务ID:</strong> <span id="pro_taskIdDisplay"></span></p>
                    <p><strong>总体状态:</strong> <span id="pro_overallStatusDisplay"></span></p>
                    <p id="pro_errorMessageGlobal" class="error-msg hidden"></p>
                </div>

                <!-- Content for each professional step -->
                <div id="pro_step_content_preprocess" class="pro-step-specific-content hidden config-block">
                    <h3>数据预处理配置</h3>
                    <label for="pro_excelFile_preprocess">1. 选择原始Excel文件:</label>
                    <input type="file" id="pro_excelFile_preprocess" accept=".xlsx, .xls">
                    <div id="pro_uploadFileInfo_preprocess" class="log-area hidden" style="background-color: #e9ecef; color: #495057;"></div>
                    <div id="pro_spinner_upload_preprocess" class="spinner hidden"></div>
                    
                    <div id="pro_columnMappingContainer_preprocess">
                        <h4>列映射:</h4>
                        <div id="pro_columnMappingsDiv_preprocess"></div>
                    </div>
                    <div class="button-group">
                        <button id="pro_executeBtn_preprocess" disabled>执行数据预处理</button>
                    </div>
                    <div id="pro_results_preprocess" class="results-block hidden">
                        <h4>预处理结果:</h4>
                        <p>状态: <span id="pro_status_text_preprocess"></span></p>
                        <div class="step-output-actions">
                            <a id="pro_download_preprocess" href="#" class="hidden download-btn" target="_blank">下载预处理JSON</a>
                            <button id="pro_continue_to_richtext" class="hidden continue-btn" onclick="continueToNextStep('richtext')">继续到富文本校验</button>
                        </div>
                    </div>
                </div>

                <div id="pro_step_content_richtext" class="pro-step-specific-content hidden config-block">
                    <h3>富文本校验配置</h3>

                    <!-- 输入文件选择 -->
                    <div class="input-file-section">
                        <h4>输入文件选择:</h4>
                        <div class="file-choice-options">
                            <label>
                                <input type="radio" name="richtext_input_choice" value="previous_output" checked>
                                使用上一步输出 (<span id="richtext_previous_file_name">等待预处理完成...</span>)
                            </label>
                            <label>
                                <input type="radio" name="richtext_input_choice" value="custom_upload">
                                上传自定义JSON文件
                            </label>
                        </div>
                        <div id="richtext_custom_upload_section" class="custom-upload-section hidden">
                            <label for="pro_jsonFile_richtext">选择JSON文件:</label>
                            <input type="file" id="pro_jsonFile_richtext" accept=".json">
                            <div id="pro_uploadFileInfo_richtext" class="log-area hidden" style="background-color: #e9ecef; color: #495057;"></div>
                        </div>
                    </div>

                    <div class="button-group">
                        <button id="pro_executeBtn_richtext" disabled>执行富文本校验</button>
                    </div>

                    <div id="pro_results_richtext" class="results-block hidden">
                        <h4>富文本校验结果:</h4>
                        <p>状态: <span id="pro_status_text_richtext"></span></p>
                        <div id="pro_stats_richtext"></div>
                        <div class="step-output-actions">
                            <a id="pro_download_richtext" href="#" class="hidden download-btn" target="_blank">下载校验报告Excel</a>
                            <button id="pro_continue_to_ai" class="hidden continue-btn" onclick="continueToNextStep('ai_processing')">继续到AI校验</button>
                        </div>
                    </div>
                </div>

                <div id="pro_step_content_ai_processing" class="pro-step-specific-content hidden config-block">
                    <h3>AI校验配置</h3>

                    <!-- 输入文件选择 -->
                    <div class="input-file-section">
                        <h4>输入文件选择:</h4>
                        <div class="file-choice-options">
                            <label>
                                <input type="radio" name="ai_input_choice" value="previous_output" checked>
                                使用预处理输出 (<span id="ai_previous_file_name">等待预处理完成...</span>)
                            </label>
                            <label>
                                <input type="radio" name="ai_input_choice" value="custom_upload">
                                上传自定义JSON文件
                            </label>
                        </div>
                        <div id="ai_custom_upload_section" class="custom-upload-section hidden">
                            <label for="pro_jsonFile_ai">选择JSON文件:</label>
                            <input type="file" id="pro_jsonFile_ai" accept=".json">
                            <div id="pro_uploadFileInfo_ai" class="log-area hidden" style="background-color: #e9ecef; color: #495057;"></div>
                        </div>
                    </div>

                    <h4>AI处理参数:</h4>
                    <input type="checkbox" id="pro_enableLiteralTranslation" name="pro_enableLiteralTranslation">
                    <label for="pro_enableLiteralTranslation" class="inline-label">启用中英直译</label><br>
                    <input type="checkbox" id="pro_enableDecomposition" name="pro_enableDecomposition">
                    <label for="pro_enableDecomposition" class="inline-label">启用翻译拆解</label><br>

                    <div class="button-group">
                        <button id="pro_executeBtn_ai_processing" disabled>执行AI校验</button>
                    </div>
                    <div id="pro_ai_progress_container" class="hidden">
                        <p>AI校验进度:</p>
                        <div class="progress-bar-container">
                            <div id="pro_ai_progressBar" class="progress-bar">0%</div>
                        </div>
                    </div>

                    <div id="pro_results_ai_processing" class="results-block hidden">
                        <h4>AI校验结果:</h4>
                        <p>状态: <span id="pro_status_text_ai_processing"></span></p>
                        <div class="step-output-actions">
                            <a id="pro_download_ai_processing" href="#" class="hidden download-btn" target="_blank">下载AI校验报告Excel</a>
                            <button id="pro_continue_to_scoring" class="hidden continue-btn" onclick="continueToNextStep('scoring')">继续到翻译评分</button>
                        </div>
                    </div>
                </div>

                <div id="pro_step_content_scoring" class="pro-step-specific-content hidden config-block">
                    <h3>翻译评分配置</h3>

                    <!-- 输入文件选择 -->
                    <div class="input-file-section">
                        <h4>输入文件选择:</h4>
                        <div class="file-choice-options">
                            <label>
                                <input type="radio" name="scoring_input_choice" value="previous_output" checked>
                                使用AI校验输出 (<span id="scoring_previous_file_name">等待AI校验完成...</span>)
                            </label>
                            <label>
                                <input type="radio" name="scoring_input_choice" value="custom_upload">
                                上传自定义Excel文件
                            </label>
                        </div>
                        <div id="scoring_custom_upload_section" class="custom-upload-section hidden">
                            <label for="pro_excelFile_scoring">选择Excel文件:</label>
                            <input type="file" id="pro_excelFile_scoring" accept=".xlsx, .xls">
                            <div id="pro_uploadFileInfo_scoring" class="log-area hidden" style="background-color: #e9ecef; color: #495057;"></div>
                        </div>
                    </div>

                    <div class="button-group">
                        <button id="pro_executeBtn_scoring" disabled>执行翻译评分</button>
                    </div>

                    <div id="pro_results_scoring" class="results-block hidden">
                        <h4>翻译评分结果:</h4>
                        <p>状态: <span id="pro_status_text_scoring"></span></p>
                        <div id="pro_stats_scoring"></div>
                        <div class="step-output-actions">
                            <a id="pro_download_scoring" href="#" class="hidden download-btn" target="_blank">下载最终评分报告Excel</a>
                            <div id="pro_workflow_complete" class="hidden workflow-complete">
                                <p class="success-msg">🎉 专业模式工作流程已完成！</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="button-group" style="margin-top: 30px;">
                    <button id="pro_resetTaskButton" onclick="resetProfessionalModeTask()" class="hidden">重置专业模式任务</button>
                </div>
            </div>

            <!-- Global Log Display Content -->
            <div id="log_display_content" class="content-section">
                <h2 class="section-title">后台日志</h2>
                <div class="button-group">
                    <button onclick="fetchGlobalLog()">刷新主日志</button>
                    <button onclick="fetchCurrentTaskLog()" id="fetchTaskLogBtn" disabled>刷新当前任务日志</button>
                    <button onclick="toggleAutoRefresh()" id="autoRefreshBtn">开启自动刷新</button>
                </div>
                <div class="log-controls">
                    <label>
                        <input type="radio" name="logType" value="main" checked onchange="switchLogType('main')"> 主应用日志
                    </label>
                    <label>
                        <input type="radio" name="logType" value="task" onchange="switchLogType('task')"> 当前任务日志
                    </label>
                </div>
                <div id="globalLogOutput" class="log-display-area" style="height: 500px;">点击"刷新主日志"加载日志内容...</div>
            </div>
        </div>
    </div>

    <script>
        // --- Global State & Configuration ---
        let currentMode = 'simple'; // 'simple' or 'professional'
        let currentProStep = 'preprocess'; // Active step in professional mode
        let proStepStatus = {
            preprocess: { status: 'pending', inputFile: null, outputFile: null, stats: null },
            richtext: { status: 'pending', inputFile: null, outputFile: null, stats: null },
            ai_processing: { status: 'pending', inputFile: null, outputFile: null, stats: null },
            scoring: { status: 'pending', inputFile: null, outputFile: null, stats: null }
        };

        let simpleTask = { id: null, fileId: null, fileName: null, status: 'idle', pollInterval: null, headers: [], columnMappings: {} };
        let proTask = { id: null, status: 'idle', pollInterval: null }; // For overall professional task tracking if needed, or step-specific handling
        let proAutoAdvanceEnabled = false; // Flag to control whether professional mode should auto-advance to next steps

        // Log display state
        let currentLogType = 'main'; // 'main' or 'task'
        let logAutoRefreshInterval = null;

        const logicalFields = [
            { key: "record_id", label: "记录ID", isCore: true, default_keywords: ["id", "ID", "RecordId", "记录ID", "Index", "索引"] },
            { key: "file_name", label: "文件名 (用于分类)", isCore: true, default_keywords: ["file", "文件名", "名称"] },
            { key: "source_text", label: "原文", isCore: true, default_keywords: ["source", "Source", "原文", "源文本", "Original", "English", "英文"] },
            { key: "translation_text", label: "译文", isCore: true, default_keywords: ["target", "Target", "译文", "目标文本", "Translated", "Chinese", "中文"] },
            { key: "speaker", label: "说话人", isCore: false, default_keywords: ["speaker", "说话人"] },
            { key: "comments", label: "原文件备注", isCore: false, default_keywords: ["comment", "备注", "注释"] },
            { key: "path", label: "路径", isCore: false, default_keywords: ["path", "路径"] },
            { key: "scenario", label: "场景", isCore: false, default_keywords: ["scenario", "场景"] },
            { key: "char_intro", label: "角色介绍", isCore: false, default_keywords: ["char", "角色介绍", "character intro"] },
            { key: "char_style", label: "角色语言风格", isCore: false, default_keywords: ["style", "语言风格", "character style"] },
            { key: "tone", label: "语气", isCore: false, default_keywords: ["tone", "语气"] },
            { key: "note", label: "翻译要求/注意", isCore: false, default_keywords: ["note", "要求", "注意"] }
        ];

        // --- Element Caching (Partial - more inside functions for dynamic elements) ---
        const simpleModeContent = document.getElementById('simpleModeContent');
        const professionalModeContent = document.getElementById('professionalModeContent');
        const logDisplayContent = document.getElementById('log_display_content');
        const globalLogOutput = document.getElementById('globalLogOutput');

        // --- Mode and Tab Switching ---
        function switchMode(mode) {
            currentMode = mode;
            document.getElementById('simpleModeTab').classList.toggle('active', mode === 'simple');
            document.getElementById('professionalModeTab').classList.toggle('active', mode === 'professional');
            
            simpleModeContent.classList.toggle('active', mode === 'simple');
            professionalModeContent.classList.toggle('active', mode === 'professional');
            logDisplayContent.classList.remove('active'); // Hide log when switching main modes
            document.getElementById('logTab').classList.remove('active');

            if (mode === 'professional') {
                activateProStep(currentProStep); // Show the current or default pro step content
                updateProStepIndicators();
            }
            // Reset task states or UI elements specific to the mode if necessary
        }

        function showGlobalTabContent(tabId) {
            simpleModeContent.classList.remove('active');
            professionalModeContent.classList.remove('active');
            logDisplayContent.classList.toggle('active', tabId === 'log_display_content');
            
            document.getElementById('simpleModeTab').classList.remove('active');
            document.getElementById('professionalModeTab').classList.remove('active');
            document.getElementById('logTab').classList.toggle('active', tabId === 'log_display_content');

            if (tabId === 'log_display_content') {
                fetchGlobalLog();
            }
        }

        // --- Professional Mode Step Management ---
        function updateProStepIndicators() {
            Object.keys(proStepStatus).forEach(stepKey => {
                const item = document.getElementById(`pro_step_${stepKey}`);
                if (!item) return;
                item.classList.remove('pending', 'running', 'completed', 'failed', 'active-step');
                item.classList.add(proStepStatus[stepKey].status);
                if (stepKey === currentProStep) {
                    item.classList.add('active-step');
                }
            });
        }

        function activateProStep(stepKey) {
            currentProStep = stepKey;
            document.querySelectorAll('.pro-step-specific-content').forEach(el => el.classList.add('hidden'));
            const activeContent = document.getElementById(`pro_step_content_${stepKey}`);
            if (activeContent) {
                activeContent.classList.remove('hidden');
            }
            updateProStepIndicators();
            
            // Auto-fill input for next step if previous output exists
            if (proTask.id) { // Only if a task is active
                if (stepKey === 'richtext' && proStepStatus.preprocess.outputFile) {
                    document.getElementById('pro_jsonFile_richtext').previousElementSibling.textContent = `1. 预处理后的JSON文件 (来自任务 ${proTask.id}): ${getFilenameFromPath(proStepStatus.preprocess.outputFile)}`;
                    // Potentially disable file input or mark as auto-filled
                } else if (stepKey === 'ai_processing' && proStepStatus.preprocess.outputFile) {
                     document.getElementById('pro_jsonFile_ai').previousElementSibling.textContent = `1. 预处理后的JSON文件 (来自任务 ${proTask.id}): ${getFilenameFromPath(proStepStatus.preprocess.outputFile)}`;
                } else if (stepKey === 'scoring' && proStepStatus.ai_processing.outputFile) {
                     document.getElementById('pro_excelFile_scoring').previousElementSibling.textContent = `1. AI校验报告 (来自任务 ${proTask.id}): ${getFilenameFromPath(proStepStatus.ai_processing.outputFile)}`;
                }
            }
        }

        function getFilenameFromPath(path) {
            return path ? path.split('/').pop().split('_').pop() : '未知文件';
        }

        function getNextStepKey(currentStepKey) {
            const stepOrder = ['preprocess', 'richtext', 'ai_processing', 'scoring'];
            const currentIndex = stepOrder.indexOf(currentStepKey);
            if (currentIndex >= 0 && currentIndex < stepOrder.length - 1) {
                return stepOrder[currentIndex + 1];
            }
            return null;
        }

        function getNextStepDisplayName(stepKey) {
            const stepNames = {
                'richtext': '富文本校验',
                'ai_processing': 'AI校验',
                'scoring': '翻译评分'
            };
            return stepNames[stepKey] || stepKey;
        }

        function getCustomFileForStep(stepKey) {
            // Check if user has selected custom upload and has uploaded a file for this step
            let inputChoiceName = '';
            if (stepKey === 'richtext') {
                inputChoiceName = 'richtext_input_choice';
            } else if (stepKey === 'ai_processing') {
                inputChoiceName = 'ai_input_choice';
            } else if (stepKey === 'scoring') {
                inputChoiceName = 'scoring_input_choice';
            }

            if (inputChoiceName) {
                const selectedChoice = document.querySelector(`input[name="${inputChoiceName}"]:checked`);
                if (selectedChoice && selectedChoice.value === 'custom_upload') {
                    const stepInputFile = proStepStatus[stepKey].inputFile;
                    if (stepInputFile && stepInputFile.file_id) {
                        return {
                            file_id: stepInputFile.file_id,
                            filename: stepInputFile.filename
                        };
                    }
                }
            }
            return null;
        }

        // --- File Upload & Column Mapping (Common Logic, adapted for each mode) ---
        function setupFileUploadListener(fileInputId, infoDivId, spinnerId, modePrefix = 'simple') {
            const fileInput = document.getElementById(fileInputId);
            const infoDiv = document.getElementById(infoDivId);
            const spinner = document.getElementById(spinnerId);
            // Get specific buttons related to the mode/step
            const simpleStartBtn = (modePrefix === 'simple') ? document.getElementById('simple_startProcessingBtn') : null;
            const proExecuteBtnForCurrentStep = (modePrefix === 'pro') ? document.getElementById(`pro_executeBtn_${currentProStep}`) : null;
            const proGotoParamsBtn = (modePrefix === 'pro' && fileInputId === 'pro_excelFile_preprocess') ? document.getElementById('goto_params_button') : null;

            fileInput.addEventListener('change', async function(event) {
                const file = event.target.files[0];
                if (!file) return;

                infoDiv.textContent = `正在上传文件: ${file.name} ...`;
                infoDiv.classList.remove('hidden');
                spinner.classList.remove('hidden');
                if(simpleStartBtn) simpleStartBtn.disabled = true;
                if(proExecuteBtnForCurrentStep) proExecuteBtnForCurrentStep.disabled = true;
                if(proGotoParamsBtn) proGotoParamsBtn.disabled = true;

                if (modePrefix === 'simple') {
                    simpleTask.fileId = null; simpleTask.fileName = null; simpleTask.headers = []; simpleTask.columnMappings = {};
                    document.getElementById('simple_columnMappingSectionContainer').classList.add('hidden');
                    document.getElementById('simple_columnMappingsDiv').innerHTML = '';
                    document.getElementById('simple_params_config_area').classList.add('hidden');
                } else if (modePrefix === 'pro') {
                    // If it's the main file upload for pro mode, reset global task file IDs as well
                    if (fileInputId === 'pro_excelFile_preprocess') {
                        currentFileId = null; currentFileName = null; currentHeaders = [];
                        document.getElementById('pro_columnMappingsDiv_preprocess').innerHTML = '';
                        // currentColumnMappings (global for pro mode step 2) will be repopulated by populateColumnMappingUI
                    }
                    // For any pro step file upload, clear its specific input in proStepStatus
                    const stepKeyForProInput = fileInputId.replace('pro_excelFile_', '').replace('pro_jsonFile_', '');
                    if (proStepStatus[stepKeyForProInput]) {
                        proStepStatus[stepKeyForProInput].inputFile = null;
                    }
                }

                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await fetch('/upload/', { method: 'POST', body: formData });
                    const data = await response.json();
                    spinner.classList.add('hidden');

                    if (response.ok) {
                        infoDiv.textContent = `文件上传成功: ${data.filename}.`;
                        infoDiv.classList.remove('error-msg'); infoDiv.classList.add('success-msg');
                        
                        if (modePrefix === 'simple') {
                            simpleTask.fileId = data.file_id;
                            simpleTask.fileName = data.filename;
                            simpleTask.headers = data.headers || [];
                            document.getElementById('simple_columnMappingSectionContainer').classList.remove('hidden');
                            populateColumnMappingUI(simpleTask.headers, 'simple_columnMappingsDiv', 'simple');
                            document.getElementById('simple_params_config_area').classList.remove('hidden');
                            if(simpleStartBtn) simpleStartBtn.disabled = false;
                            console.log("[Simple Upload Success] simpleTask.fileId:", simpleTask.fileId, "simpleTask.fileName:", simpleTask.fileName);
                        } else if (modePrefix === 'pro') {
                            const stepKeyForProInput = fileInputId.replace('pro_excelFile_', '').replace('pro_jsonFile_', '');
                            
                            if (fileInputId === 'pro_excelFile_preprocess') { 
                                currentFileId = data.file_id;     
                                currentFileName = data.filename;  
                                currentHeaders = data.headers || [];
                                
                                if (proStepStatus['preprocess']) {
                                    proStepStatus['preprocess'].inputFile = { file_id: data.file_id, filename: data.filename, headers: currentHeaders, path: data.saved_path, originalElementId: fileInputId };
                                }
                                populateColumnMappingUI(currentHeaders, 'pro_columnMappingsDiv_preprocess', 'pro_preprocess'); // Use specific ID for preprocess's mapping div
                                document.getElementById('pro_columnMappingContainer_preprocess').classList.remove('hidden'); // This is the container FOR preprocess step

                                if (proGotoParamsBtn) proGotoParamsBtn.disabled = false; 
                                console.log("[Pro Upload Success - Step 2 Main File] Globals set. currentFileId:", currentFileId, "currentFileName:", currentFileName);
                                console.log("[Pro Upload Success - Step 2 Main File] proGotoParamsBtn.disabled:", proGotoParamsBtn ? proGotoParamsBtn.disabled : 'N/A');
                            } else if (proStepStatus[stepKeyForProInput]) {
                                // For files uploaded within other specific pro steps (not the main Step 2 upload)
                                proStepStatus[stepKeyForProInput].inputFile = { file_id: data.file_id, filename: data.filename, headers: data.headers || [], path: data.saved_path, originalElementId: fileInputId };
                                console.log(`[Pro Upload Success - Step ${stepKeyForProInput} File] Input set for step ${stepKeyForProInput}`);

                                // Update step input availability after file upload
                                updateStepInputAvailability();
                            }

                            // Enable the execute button for the *current active pro step* if a file was just uploaded for it
                            if (fileInputId.includes(currentProStep)) { // Check if the upload was for the currently active pro step
                                const execBtnForActiveStep = document.getElementById(`pro_executeBtn_${currentProStep}`);
                                if(execBtnForActiveStep) execBtnForActiveStep.disabled = false;
                            }
                        }
                    } else {
                        infoDiv.textContent = `文件上传失败: ${data.detail || response.statusText}`;
                        infoDiv.classList.add('error-msg'); infoDiv.classList.remove('success-msg');
                        if (modePrefix === 'pro' && fileInputId === 'pro_excelFile_preprocess') {
                            currentFileId = null; currentFileName = null; // Reset globals if main pro upload fails
                        }
                    }
                } catch (error) {
                    spinner.classList.add('hidden');
                    console.error('Upload error for ' + fileInputId + ':', error);
                    infoDiv.textContent = `文件上传出错: ${error.message}`;
                    infoDiv.classList.add('error-msg'); infoDiv.classList.remove('success-msg');
                    if (modePrefix === 'pro' && fileInputId === 'pro_excelFile_preprocess') {
                        currentFileId = null; currentFileName = null; // Reset globals on error
                    }
                }
            });
        }

        function populateColumnMappingUI(headers, targetDivId, modeContext) {
            const container = document.getElementById(targetDivId);
            if (!container) {
                console.error(`[${modeContext}] populateColumnMappingUI: Target container '${targetDivId}' not found.`);
                return;
            }
            container.innerHTML = ''; 
            
            let localStepMappings = {}; // Use a local temporary object for this population run

            if (!headers || headers.length === 0) {
                container.innerHTML = '<p class="error-msg">未能从Excel文件读取表头信息，无法进行列映射。</p>';
                // Disable relevant next/execute button for this context
                if (modeContext === 'pro_preprocess') document.getElementById('pro_executeBtn_preprocess').disabled = true;
                else if (modeContext === 'simple') document.getElementById('simple_startProcessingBtn').disabled = true;
                return;
            }
            console.log(`[populateColumnMappingUI - ${modeContext}] Populating for target: ${targetDivId}. Headers:`, headers);

            logicalFields.forEach(field => {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'mapping-row';
                const label = document.createElement('label');
                const selectId = `${modeContext}_mapping_${field.key}`;
                label.htmlFor = selectId;
                label.textContent = `${field.label}`;
                if (field.isCore) {
                    const marker = document.createElement('span');
                    marker.className = 'field-core-marker';
                    marker.textContent = '*';
                    label.appendChild(marker);
                }
                const select = document.createElement('select');
                select.id = selectId; 
                
                const defaultOption = document.createElement('option');
                defaultOption.value = ""; 
                defaultOption.textContent = field.isCore ? "选择Excel列... (核心)" : "选择Excel列 (可选)";
                select.appendChild(defaultOption);

                headers.forEach(header => {
                    const option = document.createElement('option');
                    const headerStr = String(header); 
                    option.value = headerStr;
                    option.textContent = headerStr;
                    select.appendChild(option);
                });
                
                let preSelectedValue = "";
                const currentFieldKeywords = field.default_keywords || [];
                if (currentFieldKeywords.length > 0) {
                    for (const keyword of currentFieldKeywords) {
                        if (typeof keyword !== 'string') continue; 
                        const lowerKeyword = keyword.toLowerCase();
                        for (const header of headers) { 
                            const headerStr = String(header);
                            if (headerStr.toLowerCase().includes(lowerKeyword)) {
                                preSelectedValue = headerStr; break;
                            }
                        }
                        if (preSelectedValue) break;
                    }
                }
                if (!preSelectedValue && field.key && headers.map(h => String(h)).includes(field.key)) {
                    preSelectedValue = field.key; 
                }
                select.value = preSelectedValue; 

                if (select.value && select.value !== "") {
                    localStepMappings[field.key] = select.value;
                }

                select.onchange = (event) => {
                    if (event.target.value && event.target.value !== "") { 
                        localStepMappings[field.key] = event.target.value;
                    } else {
                        delete localStepMappings[field.key];
                    }
                    // Update the correct global/mode-specific mapping object based on modeContext
                    if (modeContext === 'pro_preprocess') {
                        currentColumnMappings = {...localStepMappings};
                        console.log(`[onChange - pro_preprocess] Global currentColumnMappings updated. Field: ${field.key}, New Value: ${event.target.value}, All Mappings:`, JSON.stringify(currentColumnMappings));
                    } else if (modeContext === 'simple') {
                        simpleTask.columnMappings = {...localStepMappings};
                        console.log(`[onChange - simple] simpleTask.columnMappings updated. Field: ${field.key}, New Value: ${event.target.value}, All Mappings:`, JSON.stringify(simpleTask.columnMappings));
                    }
                };
                rowDiv.appendChild(label); rowDiv.appendChild(select);
                container.appendChild(rowDiv);
            });
            
            // Final assignment after all fields are processed for the given context
            if (modeContext === 'pro_preprocess') {
                currentColumnMappings = {...localStepMappings}; // Ensure global is set after loop
                console.log(`[populateColumnMappingUI - pro_preprocess] Final global currentColumnMappings set:`, JSON.stringify(currentColumnMappings));
            } else if (modeContext === 'simple') {
                simpleTask.columnMappings = {...localStepMappings};
                 console.log(`[populateColumnMappingUI - simple] Final simpleTask.columnMappings set:`, JSON.stringify(simpleTask.columnMappings));
            }
        }

        function getSelectedColumnMappingsForPayload(validationContext = 'professional') {
            let mappingsToValidate = {};
            let contextForLog = validationContext;

            if (validationContext === 'pro_preprocess' || validationContext === 'professional_main') {
                mappingsToValidate = currentColumnMappings;
                contextForLog = `professional (currentColumnMappings for ${validationContext})`;
            } else if (validationContext === 'simple') {
                mappingsToValidate = simpleTask.columnMappings;
                contextForLog = 'simple (simpleTask.columnMappings)';
            } else {
                console.error("[getSelectedColumnMappingsForPayload] Unknown validationContext:", validationContext);
                mappingsToValidate = {};
            }

            let allCoreFieldsMapped = true;
            let missingCoreFieldsInfo = []; // Store more info for debugging

            console.log(`[getSelectedColumnMappings - ${contextForLog}] Starting validation. Mappings to validate:`, JSON.stringify(mappingsToValidate));

            logicalFields.forEach(field => {
                if (field.isCore) {
                    const mappingValue = mappingsToValidate[field.key];
                    const isMapped = mappingsToValidate.hasOwnProperty(field.key) && mappingValue !== "" && mappingValue !== null && mappingValue !== undefined;
                    
                    // Log details for each core field
                    console.log(`[getSelectedColumnMappings - ${contextForLog}] Validating Core Field: '${field.label}' (key: ${field.key}). Value in mappings: '${mappingValue}'. Is Mapped: ${isMapped}`);

                    if (!isMapped) {
                        allCoreFieldsMapped = false;
                        missingCoreFieldsInfo.push({ label: field.label, key: field.key, valueFound: mappingValue });
                    }
                }
            });

            console.log(`[getSelectedColumnMappings - ${contextForLog}] Overall validation - All Core Fields Mapped: ${allCoreFieldsMapped}`);
            if (!allCoreFieldsMapped) {
                console.log(`[getSelectedColumnMappings - ${contextForLog}] Missing/Empty Core Fields Details:`, missingCoreFieldsInfo);
            }

            if (!allCoreFieldsMapped) {
                const missingLabels = missingCoreFieldsInfo.map(f => f.label).join(', ');
                alert(`${validationContext.includes('simple') ? '简单' : '专业'}模式下，请确保所有标记为核心 (*) 的字段都已选择对应的Excel列！\n未正确映射的核心字段: ${missingLabels}`);
                return 'VALIDATION_ERROR';
            }

            if (Object.keys(mappingsToValidate).length > 0) {
                console.log(`[getSelectedColumnMappings - ${contextForLog}] Validation successful. Returning mappings:`, JSON.stringify(mappingsToValidate));
                return mappingsToValidate;
            } else {
                // This case should ideally only be hit if there are NO logicalFields defined as core,
                // or if the user cleared all mappings (including core ones, which should have failed above).
                // If it means "no mappings made at all", backend should use defaults.
                console.log(`[getSelectedColumnMappings - ${contextForLog}] No mappings to return (empty or all cleared), returning null for backend defaults.`);
                return null;
            }
        }
        
        // --- Event Listener for Pro Preprocess Execute Button ---
        const proExecuteBtnPreprocess = document.getElementById('pro_executeBtn_preprocess');
        if (proExecuteBtnPreprocess) {
            proExecuteBtnPreprocess.addEventListener('click', async () => {
                console.log("[Pro Preprocess Execute Button] Clicked.");
                // Validate and get column mappings specifically for the 'pro_preprocess' context
                const proColMapsForExecution = getSelectedColumnMappingsForPayload('pro_preprocess');
                
                if (proColMapsForExecution === 'VALIDATION_ERROR') {
                    console.log("[Pro Preprocess Execute Button] Column mapping validation failed. Aborting step execution.");
                    return; 
                }
                console.log("[Pro Preprocess Execute Button] Column mappings for preprocess step are valid:", proColMapsForExecution);
                // Pass the validated mappings directly to the execution function
                await executeProStepInternal('preprocess', proColMapsForExecution); 
            });
        }
        
        // In startProcessingBtn listener (global one, now called initiate_processing_button)
        // Ensure it uses the correct context for getSelectedColumnMappingsForPayload
        const initiateProcessingButton = document.getElementById('initiate_processing_button');
        if(initiateProcessingButton) {
            initiateProcessingButton.addEventListener('click', async function() {
                if (!currentFileId || !currentFileName) {
                    alert('请先在"文件与列配置"步骤上传文件!');
                    navigateToStep('file_config');
                    return;
                }
                const selectedMode = processingModeSelect.value;
                const validationContextForMain = selectedMode === 'professional' ? 'professional_main' : 'simple';
                const finalColumnMappings = getSelectedColumnMappingsForPayload(validationContextForMain);
                
                if (finalColumnMappings === 'VALIDATION_ERROR') {
                    if (selectedMode === 'professional') navigateToStep('file_config'); // Go back to fix pro mappings
                    // For simple mode, if mapping UI was shown and failed, user needs to fix it there too.
                    // Or, if simple mode allows proceeding with null mappings (backend defaults), this logic might differ.
                    return;
                }
                // ... rest of initiateProcessing logic ...
                 this.disabled = true;
                 spinnerProcessing.classList.remove('hidden');
                 resetTaskUIDetails(); 

                 const params = {
                    enable_literal_translation: document.getElementById('enable_literal_translation').checked,
                    enable_decomposition: document.getElementById('enable_decomposition').checked,
                    skip_scoring: (selectedMode === 'professional') ? document.getElementById('skip_scoring').checked : false,
                    column_mappings: finalColumnMappings 
                };
                 const payload = { file_id: currentFileId, filename: currentFileName, mode: selectedMode, params: params };
            
                 navigateToStep('processing_status'); 
                 try {
                    const response = await fetch('/initiate_processing/', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    const data = await response.json();
                    if (response.ok) {
                        currentTaskId = data.task_id;
                        taskIdDisplay.textContent = currentTaskId;
                        taskModeDisplay.textContent = selectedMode;
                        taskInfoDiv.classList.remove('hidden');
                        overallStatusDisplay.textContent = "任务已启动，正在获取状态...";
                        if (statusInterval) clearInterval(statusInterval);
                        statusInterval = setInterval(fetchTaskStatus, 3000);
                        fetchTaskStatus(); 
                    } else {
                        errorMessageDisplay.textContent = `任务启动失败: ${data.detail ? JSON.stringify(data.detail) : response.statusText}`;
                        errorMessageDisplay.classList.remove('hidden');
                        this.disabled = false; 
                        spinnerProcessing.classList.add('hidden');
                    }
                } catch (error) {
                    console.error('Initiate processing error:', error);
                    errorMessageDisplay.textContent = `任务启动出错: ${error.message}`;
                    errorMessageDisplay.classList.remove('hidden');
                    this.disabled = false;
                    spinnerProcessing.classList.add('hidden');
                }
            });
        }

        // --- Professional Mode Logic (per step execution) ---
        function setupProStepExecution() {
            ['preprocess', 'richtext', 'ai_processing', 'scoring'].forEach(stepKey => {
                const execBtn = document.getElementById(`pro_executeBtn_${stepKey}`);
                if (execBtn) {
                    execBtn.addEventListener('click', async () => {
                        await executeProStepInternal(stepKey);
                    });
                }
            });
        }
        
        async function executeProStepInternal(stepKey, columnMappingsForThisStep = null) {
            console.log(`[executeProStepInternal] Called for step: ${stepKey}, proTask.id: ${proTask.id}, timestamp: ${new Date().toISOString()}`);

            // Prevent multiple simultaneous executions of the same step
            const execBtn = document.getElementById(`pro_executeBtn_${stepKey}`);
            if (execBtn && execBtn.disabled) {
                console.log(`[executeProStepInternal] Step ${stepKey} already executing, ignoring duplicate request.`);
                return;
            }

            // Disable the execute button to prevent duplicate submissions
            if (execBtn) {
                execBtn.disabled = true;
                console.log(`[executeProStepInternal] Disabled execute button for step: ${stepKey}`);
            }

            try {
                // 1. Ensure Pro Task ID exists or initiate one for the first step
                if (!proTask.id && stepKey === 'preprocess' && proStepStatus.preprocess.inputFile) {
                const proFileId = proStepStatus.preprocess.inputFile.file_id;
                const proFileName = proStepStatus.preprocess.inputFile.filename;
                
                // Use the columnMappingsForThisStep passed to the function for preprocess
                // No need to call getSelectedColumnMappingsForPayload again here for preprocess initial run
                console.log("[executeProStepInternal - preprocess init] Using passed column mappings:", columnMappingsForThisStep);

                const initialParams = {
                    column_mappings: columnMappingsForThisStep, // Use the mappings passed in
                    enable_literal_translation: false, 
                    enable_decomposition: false,   
                    skip_scoring: false            
                };
                const payload = { file_id: proFileId, filename: proFileName, mode: 'professional', params: initialParams };
                
                document.getElementById(`pro_status_text_${stepKey}`).textContent = "任务初始化中...";
                try {
                    const response = await fetch('/initiate_processing/', {
                        method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload)
                    });
                    const data = await response.json();
                    if (response.ok) {
                        proTask.id = data.task_id;
                        document.getElementById('pro_taskIdDisplay').textContent = proTask.id;
                        document.getElementById('pro_task_info_overall').classList.remove('hidden');
                        // Backend will set the task status upon initiation, poller will pick it up.
                        // We can optimistically set the step UI here or wait for poller.
                        proStepStatus[stepKey].status = 'queued'; // Or 'running' if backend starts it immediately
                        updateProStepIndicators();
                        document.getElementById(`pro_status_text_${stepKey}`).textContent = "任务已提交，等待执行...";

                        // Update task log button state
                        updateTaskLogButtonState();

                        if (proTask.pollInterval) clearInterval(proTask.pollInterval);
                        proTask.pollInterval = setInterval(() => fetchTaskStatusInternal('professional'), 3000);
                        fetchTaskStatusInternal('professional');
                    } else {
                        document.getElementById(`pro_status_text_${stepKey}`).textContent = `错误: ${data.detail || '初始化任务失败'}`;
                        proStepStatus[stepKey].status = 'failed'; updateProStepIndicators();
                        return; 
                    }
                } catch (err) {
                    document.getElementById(`pro_status_text_${stepKey}`).textContent = `错误: ${err.message}`;
                    proStepStatus[stepKey].status = 'failed'; updateProStepIndicators();
                    return;
                }
            } else if (proTask.id) {
                // Subsequent steps for an existing professional task OR retrying preprocess after task exists
                proStepStatus[stepKey].status = 'running';
                updateProStepIndicators();
                document.getElementById(`pro_status_text_${stepKey}`).textContent = "发送执行请求...";
                if (stepKey === 'ai_processing') {
                    document.getElementById('pro_ai_progress_container').classList.remove('hidden');
                    document.getElementById('pro_ai_progressBar').style.width = '0%';
                    document.getElementById('pro_ai_progressBar').textContent = '0%';
                }

                let stepPayload = {
                    step_name: stepKey  // Explicitly specify which step to execute
                };

                // Check if user wants to use custom uploaded file for this step
                const customFileInfo = getCustomFileForStep(stepKey);
                if (customFileInfo) {
                    stepPayload.file_id = customFileInfo.file_id;
                    stepPayload.filename = customFileInfo.filename;
                    console.log(`[executeProStepInternal] Using custom file for step ${stepKey}:`, customFileInfo);
                }

                if (stepKey === 'ai_processing') { // AI step might have specific params to pass
                    stepPayload.params = {
                        enable_literal_translation: document.getElementById('pro_enableLiteralTranslation').checked,
                        enable_decomposition: document.getElementById('pro_enableDecomposition').checked
                        // skip_scoring is a task-level param, not step-specific for AI step here
                    };
                }
                // If columnMappingsForThisStep was provided (e.g., for a retried preprocess on existing task)
                // And this specific step allows column mapping override, include it.
                // For now, assuming only initial preprocess uses columnMappingsForThisStep argument directly for task creation.
                // Subsequent calls to /next_step for an *existing* task might not need to resend mappings if backend stores them.
                // Or if a step allows re-upload and re-map, then `columnMappingsForThisStep` would be relevant for that payload.

                try {
                    const response = await fetch(`/process/${proTask.id}/next_step`, {
                         method: 'POST',
                         headers: { 'Content-Type': 'application/json' }, 
                         body: Object.keys(stepPayload).length > 0 ? JSON.stringify(stepPayload) : null
                    });
                    const data = await response.json();
                    if (response.ok) {
                        document.getElementById(`pro_status_text_${stepKey}`).textContent = "执行中 (等待后端确认)...";
                        fetchTaskStatusInternal('professional'); 
                    } else {
                        document.getElementById(`pro_status_text_${stepKey}`).textContent = `启动步骤失败: ${data.detail}`;
                        proStepStatus[stepKey].status = 'failed';
                        updateProStepIndicators();
                    }
                } catch (err) {
                    document.getElementById(`pro_status_text_${stepKey}`).textContent = `启动步骤错误: ${err.message}`;
                    proStepStatus[stepKey].status = 'failed';
                    updateProStepIndicators();
                }
            } else {
                alert("请先上传专业模式的预处理步骤所需的文件，并确保任务已初始化。");
            }
            } finally {
                // Re-enable the button after a short delay to prevent rapid clicking
                // but allow retry if needed
                setTimeout(() => {
                    if (execBtn && (proStepStatus[stepKey].status === 'failed' || proStepStatus[stepKey].status === 'completed')) {
                        execBtn.disabled = false;
                    }
                }, 1000);
            }
        }

        function resetProfessionalModeTask() {
            if (proTask.pollInterval) clearInterval(proTask.pollInterval);
            proTask = { id: null, status: 'idle', pollInterval: null };
            currentProStep = 'preprocess';
            proStepStatus = {
                preprocess: { status: 'pending', inputFile: null, outputFile: null, stats: null },
                richtext: { status: 'pending', inputFile: null, outputFile: null, stats: null },
                ai_processing: { status: 'pending', inputFile: null, outputFile: null, stats: null },
                scoring: { status: 'pending', inputFile: null, outputFile: null, stats: null }
            };
            document.getElementById('pro_task_info_overall').classList.add('hidden');
            document.querySelectorAll('.pro-step-specific-content input[type="file"]').forEach(input => input.value = '');
            document.querySelectorAll('.pro-step-specific-content .log-area').forEach(el => {el.classList.add('hidden'); el.textContent='';});
            document.querySelectorAll('.pro-step-specific-content .results-block').forEach(el => el.classList.add('hidden'));
            document.querySelectorAll('.pro-step-specific-content .step-actions a').forEach(el => el.classList.add('hidden'));
            ['pro_columnMappingsDiv_preprocess'].forEach(id => document.getElementById(id).innerHTML = '');
            document.getElementById('pro_resetTaskButton').classList.add('hidden');
            activateProStep('preprocess');
        }

        // --- Common Task Status Polling and UI Update ---
        async function fetchTaskStatusInternal(mode) {
            const task = (mode === 'simple') ? simpleTask : proTask;
            if (!task.id) return;

            try {
                const response = await fetch(`/status/${task.id}`);
                const data = await response.json();
                if (!response.ok) {
                    const errorMsg = `获取 ${mode} 任务 ${task.id} 状态失败: ${data.detail || response.statusText}`;
                    if (mode === 'simple') document.getElementById('simple_errorMessage').textContent = errorMsg;
                    else document.getElementById('pro_errorMessageGlobal').textContent = errorMsg;
                    if (response.status === 404) { clearInterval(task.pollInterval); task.pollInterval = null; task.status = 'error';}
                    return;
                }

                task.status = data.status; // Update global task status for mode
                if (mode === 'simple') {
                    document.getElementById('simple_overallStatusDisplay').textContent = data.status;
                    if (data.error_message) {
                        document.getElementById('simple_errorMessage').textContent = data.error_message;
                        document.getElementById('simple_errorMessage').classList.remove('hidden');
                    } else {
                        document.getElementById('simple_errorMessage').classList.add('hidden');
                    }

                    // Simple mode AI progress (example, needs backend support in status data)
                    if (data.current_step_name === 'ai_processing' && data.status === 'running_step' && data.ai_progress) {
                        document.getElementById('simple_ai_progress_container').classList.remove('hidden');
                        const percent = Math.round(data.ai_progress.completed / data.ai_progress.total * 100);
                        document.getElementById('simple_ai_progressBar').style.width = percent + '%';
                        document.getElementById('simple_ai_progressBar').textContent = percent + '%';
                    } else {
                        document.getElementById('simple_ai_progress_container').classList.add('hidden');
                    }

                    if (data.status === 'completed' || data.status === 'failed') {
                        clearInterval(task.pollInterval); task.pollInterval = null;
                        document.getElementById('simple_startProcessingBtn').disabled = false;
                        if(data.log_file) {
                            document.getElementById('simple_downloadLog').href = `/download_log/${task.id}`;
                            document.getElementById('simple_downloadLog').classList.remove('hidden');
                        }
                        const ul = document.getElementById('simple_outputFilesUl');
                        ul.innerHTML = '';
                        if (data.output_files && data.output_files.length > 0) {
                            document.getElementById('simple_output_files_list').classList.remove('hidden');
                            data.output_files.forEach(name => {
                                const li = document.createElement('li');
                                const a = document.createElement('a');
                                a.href = `/download_simple_output/${task.id}/${name}`;
                                a.textContent = name; a.target = '_blank';
                                li.appendChild(a); ul.appendChild(li);
                            });
                        } else if (data.status === 'completed') {
                            ul.innerHTML = '<li>无输出文件。</li>';
                        }
                    }
                } else { // Professional Mode Update
                    console.log(`[fetchTaskStatusInternal] Professional mode status update: ${data.status}, current_step: ${data.current_step_name}, next_step: ${data.next_step_name}, timestamp: ${new Date().toISOString()}`);

                    document.getElementById('pro_overallStatusDisplay').textContent = data.status;
                    if (data.error_message) {
                         document.getElementById('pro_errorMessageGlobal').textContent = data.error_message;
                         document.getElementById('pro_errorMessageGlobal').classList.remove('hidden');
                    } else {
                         document.getElementById('pro_errorMessageGlobal').classList.add('hidden');
                    }
                    document.getElementById('pro_resetTaskButton').classList.remove('hidden');

                    // Update individual step cards for professional mode
                    let allStepsTerminal = true;
                    for (const stepKey in proStepStatus) {
                        const stepBackendInfo = data.step_outputs[stepKey];
                        if (stepBackendInfo) {
                            const oldStatus = proStepStatus[stepKey].status;
                            proStepStatus[stepKey].status = stepBackendInfo.status;
                            proStepStatus[stepKey].outputFile = stepBackendInfo.host_path;
                            proStepStatus[stepKey].stats = stepBackendInfo.stats;

                            // Log status changes for debugging
                            if (oldStatus !== stepBackendInfo.status) {
                                console.log(`[fetchTaskStatusInternal] Step ${stepKey} status changed: ${oldStatus} -> ${stepBackendInfo.status}`);
                            }

                            document.getElementById(`pro_status_text_${stepKey}`).textContent = stepBackendInfo.status;
                            const dlLink = document.getElementById(`pro_download_${stepKey}`);
                            if (stepBackendInfo.status === 'completed' && stepBackendInfo.host_path) {
                                dlLink.href = `/download/${task.id}/${stepKey}`;
                                dlLink.classList.remove('hidden');
                                dlLink.textContent = `下载 ${stepBackendInfo.display_name || '结果'}`;

                                // Show continue button for completed steps (except the last one)
                                const continueBtn = document.getElementById(`pro_continue_to_${getNextStepKey(stepKey)}`);
                                if (continueBtn) {
                                    continueBtn.classList.remove('hidden');
                                }

                                // Show workflow complete message for the last step
                                if (stepKey === 'scoring') {
                                    const workflowComplete = document.getElementById('pro_workflow_complete');
                                    if (workflowComplete) workflowComplete.classList.remove('hidden');
                                }

                                // Re-enable the execute button for completed steps (for retry)
                                const execBtn = document.getElementById(`pro_executeBtn_${stepKey}`);
                                if (execBtn) execBtn.disabled = false;
                        } else {
                                dlLink.classList.add('hidden');
                                // Hide continue button if step is not completed
                                const continueBtn = document.getElementById(`pro_continue_to_${getNextStepKey(stepKey)}`);
                                if (continueBtn) continueBtn.classList.add('hidden');
                            }
                            if (stepBackendInfo.stats && Object.keys(stepBackendInfo.stats).length > 0) {
                                document.getElementById(`pro_stats_${stepKey}`).innerHTML = `<pre>${JSON.stringify(stepBackendInfo.stats, null, 2)}</pre>`;
                            }
                        }
                        if (proStepStatus[stepKey].status !== 'completed' && proStepStatus[stepKey].status !== 'failed' && proStepStatus[stepKey].status !== 'skipped') {
                            allStepsTerminal = false;
                        }
                    }
                    updateProStepIndicators();
                    updateStepInputAvailability(); // Update file availability after status change

                    // Handle step completion status (new manual control mode)
                    if (data.status === 'step_completed' || data.status === 'pending_next_step') {
                        console.log(`[fetchTaskStatusInternal] Task status: ${data.status}, available_next_step: ${data.available_next_step}, next_step_name: ${data.next_step_name}`);

                        // In the new manual control mode, we don't auto-enable next step buttons
                        // Instead, users use the "Continue to Next Step" buttons that appear after each step completes
                        if (data.status === 'step_completed') {
                            console.log(`[fetchTaskStatusInternal] Step completed. User can manually choose to continue to next step.`);
                        }

                        // Legacy support for old pending_next_step behavior
                        if (data.status === 'pending_next_step' && data.next_step_name) {
                            const nextStepBtn = document.getElementById(`pro_executeBtn_${data.next_step_name}`);
                            if (nextStepBtn) {
                                console.log(`[fetchTaskStatusInternal] Legacy mode: Next step ${data.next_step_name} is ready`);

                                // Show a notification to the user that the next step is ready
                                const nextStepName = data.next_step_name;
                                const stepNames = {
                                    'preprocess': '数据预处理',
                                    'richtext': '富文本校验',
                                    'ai_processing': 'AI校验',
                                    'scoring': '翻译评分'
                                };
                                const stepDisplayName = stepNames[nextStepName] || nextStepName;

                                // Update the status text to indicate the next step is ready
                                document.getElementById(`pro_status_text_${nextStepName}`).textContent = `准备就绪 - 点击执行按钮开始${stepDisplayName}`;
                            }
                        }
                    }

                     // AI progress for professional mode
                    if (data.current_step_name === 'ai_processing' && data.status === 'running_step' && data.ai_progress) {
                        document.getElementById('pro_ai_progress_container').classList.remove('hidden');
                        const percent = Math.round(data.ai_progress.completed / data.ai_progress.total * 100);
                        document.getElementById('pro_ai_progressBar').style.width = percent + '%';
                        document.getElementById('pro_ai_progressBar').textContent = percent + '%';
                    } else if (proStepStatus.ai_processing.status !== 'running') { // Hide if AI not running
                        document.getElementById('pro_ai_progress_container').classList.add('hidden');
                    }

                    // Check for auto-advance conditions (this should NOT happen in professional mode)
                    if (data.status === 'pending_next_step' && data.next_step_name && proAutoAdvanceEnabled) {
                        console.warn(`[fetchTaskStatusInternal] Professional mode auto-advance detected! This should not happen. Status: ${data.status}, next_step: ${data.next_step_name}`);
                        // In professional mode, we should NEVER auto-advance. User must manually trigger each step.
                        // If this log appears, it means there's a bug in the logic.
                    }

                    if (data.status === 'completed' || data.status === 'failed' || allStepsTerminal) {
                        clearInterval(task.pollInterval); task.pollInterval = null;
                    } // Continue polling if overall task is e.g. pending_next_step but not all steps are terminal
                }

            } catch (error) {
                console.error(`Error polling ${mode} task ${task.id}:`, error);
            }
        }

        // --- Global Log Fetching ---
        async function fetchGlobalLog() {
            try {
                globalLogOutput.textContent = "正在加载日志...";

                const response = await fetch('/get_main_log/');
                if (response.ok) {
                    const data = await response.json();
                    if (data.error) {
                        globalLogOutput.textContent = `日志加载错误: ${data.content}`;
                    } else {
                        const logInfo = data.total_lines > data.shown_lines
                            ? `\n=== 显示最近 ${data.shown_lines} 行日志 (总共 ${data.total_lines} 行) ===\n\n`
                            : `\n=== 显示全部 ${data.total_lines} 行日志 ===\n\n`;
                        globalLogOutput.textContent = logInfo + data.content;

                        // Auto-scroll to bottom to show latest logs
                        globalLogOutput.scrollTop = globalLogOutput.scrollHeight;
                    }
                } else {
                    globalLogOutput.textContent = `无法加载日志: HTTP ${response.status} ${response.statusText}`;
                }
            } catch (err) {
                globalLogOutput.textContent = "加载日志时发生错误: " + err.message;
                console.error('Error fetching global log:', err);
            }
        }

        // --- Task-specific Log Fetching ---
        async function fetchCurrentTaskLog() {
            const currentTaskId = getCurrentTaskId();
            if (!currentTaskId) {
                globalLogOutput.textContent = "没有活动的任务，无法获取任务日志。";
                return;
            }

            try {
                globalLogOutput.textContent = "正在加载任务日志...";

                const response = await fetch(`/get_task_log/${currentTaskId}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.error) {
                        globalLogOutput.textContent = `任务日志加载错误: ${data.content}`;
                    } else {
                        const logInfo = `\n=== 任务 ${data.task_id} 的日志 ===\n\n`;
                        globalLogOutput.textContent = logInfo + data.content;

                        // Auto-scroll to bottom to show latest logs
                        globalLogOutput.scrollTop = globalLogOutput.scrollHeight;
                    }
                } else {
                    globalLogOutput.textContent = `无法加载任务日志: HTTP ${response.status} ${response.statusText}`;
                }
            } catch (err) {
                globalLogOutput.textContent = "加载任务日志时发生错误: " + err.message;
                console.error('Error fetching task log:', err);
            }
        }

        // --- Log Type Switching ---
        function switchLogType(logType) {
            currentLogType = logType;
            const taskLogBtn = document.getElementById('fetchTaskLogBtn');

            if (logType === 'task') {
                const currentTaskId = getCurrentTaskId();
                if (currentTaskId) {
                    taskLogBtn.disabled = false;
                    fetchCurrentTaskLog();
                } else {
                    taskLogBtn.disabled = true;
                    globalLogOutput.textContent = "没有活动的任务，无法显示任务日志。请先启动一个任务。";
                }
            } else {
                taskLogBtn.disabled = true;
                fetchGlobalLog();
            }
        }

        // --- Auto Refresh Toggle ---
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');

            if (logAutoRefreshInterval) {
                // Stop auto refresh
                clearInterval(logAutoRefreshInterval);
                logAutoRefreshInterval = null;
                btn.textContent = '开启自动刷新';
                btn.style.backgroundColor = '';
            } else {
                // Start auto refresh
                logAutoRefreshInterval = setInterval(() => {
                    if (currentLogType === 'main') {
                        fetchGlobalLog();
                    } else if (currentLogType === 'task') {
                        fetchCurrentTaskLog();
                    }
                }, 5000); // Refresh every 5 seconds
                btn.textContent = '停止自动刷新';
                btn.style.backgroundColor = '#28a745';
            }
        }

        // --- Helper function to get current task ID ---
        function getCurrentTaskId() {
            // Try to get task ID from simple mode first, then professional mode
            if (simpleTask.id) return simpleTask.id;
            if (proTask.id) return proTask.id;
            return null;
        }

        // --- Update task log button state when tasks change ---
        function updateTaskLogButtonState() {
            const taskLogBtn = document.getElementById('fetchTaskLogBtn');
            const currentTaskId = getCurrentTaskId();

            if (currentTaskId && currentLogType === 'task') {
                taskLogBtn.disabled = false;
            } else {
                taskLogBtn.disabled = true;
            }
        }

        // --- Simple Mode Processing ---
        async function processSimpleMode() {
            if (!simpleTask.fileId) {
                alert("请先上传Excel文件。");
                return;
            }

            const columnMappings = getSelectedColumnMappingsForPayload('simple');
            if (!columnMappings) {
                alert("请完成列映射配置。");
                return;
            }

            const params = {
                column_mappings: columnMappings,
                enable_literal_translation: document.getElementById('simple_enableLiteralTranslation').checked,
                enable_decomposition: document.getElementById('simple_enableDecomposition').checked,
                skip_scoring: document.getElementById('simple_skipScoring').checked
            };

            const payload = {
                file_id: simpleTask.fileId,
                filename: simpleTask.fileName,
                mode: 'simple',
                params: params
            };

            try {
                document.getElementById('simple_startProcessingBtn').disabled = true;
                document.getElementById('simple_status_results_area').classList.remove('hidden');
                document.getElementById('simple_overallStatusDisplay').textContent = "初始化中...";

                const response = await fetch('/initiate_processing/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                const data = await response.json();
                if (response.ok) {
                    simpleTask.id = data.task_id;
                    document.getElementById('simple_taskIdDisplay').textContent = simpleTask.id;
                    document.getElementById('simple_overallStatusDisplay').textContent = "任务已提交，等待执行...";

                    // Update task log button state
                    updateTaskLogButtonState();

                    // Start polling for status updates
                    if (simpleTask.pollInterval) clearInterval(simpleTask.pollInterval);
                    simpleTask.pollInterval = setInterval(() => fetchTaskStatusInternal('simple'), 3000);
                    fetchTaskStatusInternal('simple');
                } else {
                    document.getElementById('simple_overallStatusDisplay').textContent = `错误: ${data.detail || '初始化任务失败'}`;
                    document.getElementById('simple_startProcessingBtn').disabled = false;
                }
            } catch (err) {
                document.getElementById('simple_overallStatusDisplay').textContent = `错误: ${err.message}`;
                document.getElementById('simple_startProcessingBtn').disabled = false;
                console.error('Simple mode processing error:', err);
            }
        }

        // --- Professional Mode File Flow Management ---
        function continueToNextStep(nextStepKey) {
            console.log(`[continueToNextStep] Switching to step: ${nextStepKey}`);
            activateProStep(nextStepKey);
            updateStepInputAvailability();
        }

        function updateStepInputAvailability() {
            // Update file name displays and enable/disable execute buttons based on available inputs
            const steps = ['richtext', 'ai_processing', 'scoring'];

            steps.forEach(stepKey => {
                const executeBtn = document.getElementById(`pro_executeBtn_${stepKey}`);
                let hasValidInput = false;

                if (stepKey === 'richtext') {
                    // Check if preprocess output is available
                    if (proStepStatus.preprocess.status === 'completed' && proStepStatus.preprocess.outputFile) {
                        document.getElementById('richtext_previous_file_name').textContent = getFilenameFromPath(proStepStatus.preprocess.outputFile);
                        hasValidInput = true;
                    } else {
                        document.getElementById('richtext_previous_file_name').textContent = '等待预处理完成...';
                    }

                    // Check if custom file is uploaded
                    const customChoice = document.querySelector('input[name="richtext_input_choice"]:checked');
                    if (customChoice && customChoice.value === 'custom_upload' && proStepStatus.richtext.inputFile) {
                        hasValidInput = true;
                    }
                } else if (stepKey === 'ai_processing') {
                    // Check if preprocess output is available (AI uses preprocess output, not richtext)
                    if (proStepStatus.preprocess.status === 'completed' && proStepStatus.preprocess.outputFile) {
                        document.getElementById('ai_previous_file_name').textContent = getFilenameFromPath(proStepStatus.preprocess.outputFile);
                        hasValidInput = true;
                    } else {
                        document.getElementById('ai_previous_file_name').textContent = '等待预处理完成...';
                    }

                    // Check if custom file is uploaded
                    const customChoice = document.querySelector('input[name="ai_input_choice"]:checked');
                    if (customChoice && customChoice.value === 'custom_upload' && proStepStatus.ai_processing.inputFile) {
                        hasValidInput = true;
                    }
                } else if (stepKey === 'scoring') {
                    // Check if AI processing output is available
                    if (proStepStatus.ai_processing.status === 'completed' && proStepStatus.ai_processing.outputFile) {
                        document.getElementById('scoring_previous_file_name').textContent = getFilenameFromPath(proStepStatus.ai_processing.outputFile);
                        hasValidInput = true;
                    } else {
                        document.getElementById('scoring_previous_file_name').textContent = '等待AI校验完成...';
                    }

                    // Check if custom file is uploaded
                    const customChoice = document.querySelector('input[name="scoring_input_choice"]:checked');
                    if (customChoice && customChoice.value === 'custom_upload' && proStepStatus.scoring.inputFile) {
                        hasValidInput = true;
                    }
                }

                if (executeBtn) {
                    executeBtn.disabled = !hasValidInput;
                }
            });
        }

        function setupFileChoiceListeners() {
            // Setup radio button listeners for file input choices
            ['richtext', 'ai', 'scoring'].forEach(stepPrefix => {
                const radioButtons = document.querySelectorAll(`input[name="${stepPrefix}_input_choice"]`);
                radioButtons.forEach(radio => {
                    radio.addEventListener('change', function() {
                        const customSection = document.getElementById(`${stepPrefix}_custom_upload_section`);
                        if (this.value === 'custom_upload') {
                            customSection.classList.remove('hidden');
                        } else {
                            customSection.classList.add('hidden');
                        }
                        updateStepInputAvailability();
                    });
                });
            });
        }

        // --- Initialization ---
        window.onload = () => {
            switchMode('simple'); // Default to simple mode
            setupFileUploadListener('simple_excelFile', 'simple_uploadFileInfo', 'simple_spinner_upload', 'simple');
            setupFileUploadListener('pro_excelFile_preprocess', 'pro_uploadFileInfo_preprocess', 'pro_spinner_upload_preprocess', 'pro');

            // Setup file upload listeners for step-specific uploads
            setupFileUploadListener('pro_jsonFile_richtext', 'pro_uploadFileInfo_richtext', null, 'pro');
            setupFileUploadListener('pro_jsonFile_ai', 'pro_uploadFileInfo_ai', null, 'pro');
            setupFileUploadListener('pro_excelFile_scoring', 'pro_uploadFileInfo_scoring', null, 'pro');

            setupProStepExecution();
            setupSimpleModeExecution();
            setupFileChoiceListeners();
            resetProfessionalModeTask(); // Initialize pro mode state
        };

        function setupSimpleModeExecution() {
            const simpleStartBtn = document.getElementById('simple_startProcessingBtn');
            if (simpleStartBtn) {
                simpleStartBtn.addEventListener('click', processSimpleMode);
            }
        }

    </script>
</body>
</html> 