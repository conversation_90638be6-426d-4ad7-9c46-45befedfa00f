import streamlit as st
import pandas as pd
import re
from collections import Counter

def extract_quality_ratings_scorer(analysis_text):
    if not analysis_text or not isinstance(analysis_text, str): 
        return []
    
    pattern = r'【质量】：\s*([^;；\n]+)'
    matches = re.findall(pattern, analysis_text)
    
    valid_ratings = ['不合格', '合格', '良好', '优秀']
    extracted_ratings = []
    for match in matches:
        cleaned_match = match.strip()
        if cleaned_match in valid_ratings:
            extracted_ratings.append(cleaned_match)
    return extracted_ratings

def determine_final_rating_scorer(ratings):
    if not ratings: return '无评价'
    if '不合格' in ratings: return '不合格'
    counter = Counter(ratings)
    priority = {'优秀': 3, '良好': 2, '合格': 1}
    sorted_ratings = sorted(counter.items(), key=lambda x: (x[1], priority.get(x[0], 0)), reverse=True)
    return sorted_ratings[0][0] if sorted_ratings else '无评价'

def run_translation_scoring_core(input_df):
    if input_df.empty: st.info("Scorer: 输入DataFrame为空。"); return input_df
    if "翻译质量分析" not in input_df.columns:
        st.error("Scorer: 输入DataFrame缺少 '翻译质量分析' 列。"); return input_df 
    scored_df = input_df.copy()
    if "评价" not in scored_df.columns: scored_df["评价"] = ""
    total_rows = len(scored_df)
    progress_bar_score = st.progress(0.0); status_text_score = st.empty(); status_text_score.text("开始AI翻译评分...")
    for idx, row in scored_df.iterrows():
        analysis_text = row["翻译质量分析"]
        ratings = extract_quality_ratings_scorer(analysis_text)
        final_rating = determine_final_rating_scorer(ratings)
        scored_df.loc[idx, "评价"] = final_rating
        progress = (idx + 1) / total_rows
        progress_bar_score.progress(progress); status_text_score.text(f"AI翻译评分中... {idx+1}/{total_rows} ({progress:.0%})")
    status_text_score.text("AI翻译评分完成！"); progress_bar_score.empty()
    if "评价" in scored_df.columns:
        rating_counts = scored_df["评价"].value_counts()
        st.markdown("##### AI翻译评分结果统计:"); st.dataframe(rating_counts)
    return scored_df
