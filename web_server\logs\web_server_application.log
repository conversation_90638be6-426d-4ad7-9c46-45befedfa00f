2025-06-03 14:16:04,710 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 14:46:34,436 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: ed084753-160c-4371-b788-3a8208ca1548) to 'uploads/ed084753-160c-4371-b788-3a8208ca1548/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 14:46:34,607 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 14:50:49,384 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 1715dbff-11bc-4d80-bb6e-dc882ac8b7ce) to 'uploads/1715dbff-11bc-4d80-bb6e-dc882ac8b7ce/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 14:50:49,408 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:16:38,769 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:16:45,060 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 198a03d4-aea2-42a5-bcb0-4b5b2929e1eb) to 'uploads/198a03d4-aea2-42a5-bcb0-4b5b2929e1eb/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:16:45,207 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:24:43,718 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:24:52,325 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: ea2f8e08-6dda-47ec-91ec-e09103ab73e6) to 'uploads/ea2f8e08-6dda-47ec-91ec-e09103ab73e6/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:24:52,549 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:25:03,970 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:25:04,828 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:26:29,457 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:26:41,881 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 9bb8531b-cab0-4d9c-9e52-d790952b57b5) to 'uploads/9bb8531b-cab0-4d9c-9e52-d790952b57b5/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:26:42,013 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:27:19,054 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:27:19,827 - WebServerAppLogger - INFO - [main_web.log_validation_error_detail_middleware:91] - VALIDATION ERROR DETAILS for /initiate_processing/: {'detail': [{'type': 'dict_type', 'loc': ['body', 'params', 'column_mappings'], 'msg': 'Input should be a valid dictionary', 'input': 'VALIDATION_ERROR', 'url': 'https://errors.pydantic.dev/2.11/v/dict_type'}]}
2025-06-03 15:30:05,836 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-03 15:30:14,432 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: bc45e762-7e75-487b-83a8-bb1956f703bc) to 'uploads/bc45e762-7e75-487b-83a8-bb1956f703bc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:30:14,567 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-03 15:30:18,570 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:552] - Initiating task 6961cca9-9981-4243-bd4b-871e6d4e082c with payload: {'file_id': 'bc45e762-7e75-487b-83a8-bb1956f703bc', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-03 15:30:18,589 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:562] - Task 6961cca9-9981-4243-bd4b-871e6d4e082c: Copied initial file from uploads/bc45e762-7e75-487b-83a8-bb1956f703bc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/6961cca9-9981-4243-bd4b-871e6d4e082c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-03 15:30:18,595 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:591] - Task 6961cca9-9981-4243-bd4b-871e6d4e082c fully initialized in task_statuses. Mode: professional
2025-06-03 15:30:18,596 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:596] - Task 6961cca9-9981-4243-bd4b-871e6d4e082c: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-03 15:30:18,598 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:552] - Initiating task 453f4f1e-c290-4f84-b2d7-618b2d830a32 with payload: {'file_id': 'bc45e762-7e75-487b-83a8-bb1956f703bc', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': None}}
2025-06-03 15:30:18,616 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:562] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Copied initial file from uploads/bc45e762-7e75-487b-83a8-bb1956f703bc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-03 15:30:18,620 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:591] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32 fully initialized in task_statuses. Mode: professional
2025-06-03 15:30:18,621 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:596] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-03 15:30:22,978 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'preprocess'.
2025-06-03 15:30:22,980 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:30:22,982 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'preprocess'
2025-06-03 15:30:22,983 - WebServerAppLogger - INFO - [main_web.execute_professional_step:307] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'preprocess': Using initial task file: '/usr/src/web_app/outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:30:22,986 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'preprocess': Determined input file: '/usr/src/web_app/outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:30:23,036 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'preprocess' processing finished. Status: completed
2025-06-03 15:30:23,037 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'preprocess' ended with success: True
2025-06-03 15:30:23,037 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'preprocess' successful. Next step is 'richtext'.
2025-06-03 15:30:24,372 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'richtext'.
2025-06-03 15:30:24,373 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:30:24,375 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'richtext'
2025-06-03 15:30:24,378 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'richtext': Determined input file: 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/preprocessed_output.json'
2025-06-03 15:30:24,412 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'richtext' processing finished. Status: completed
2025-06-03 15:30:24,413 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'richtext' ended with success: True
2025-06-03 15:30:24,414 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'richtext' successful. Next step is 'ai_processing'.
2025-06-03 15:30:25,497 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'ai_processing'.
2025-06-03 15:30:25,498 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:30:25,500 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'ai_processing'
2025-06-03 15:30:25,503 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'ai_processing': Determined input file: 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/preprocessed_output.json'
2025-06-03 15:30:25,508 - WebServerAppLogger - INFO - [main_web.execute_professional_step:355] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32 'ai_processing': AI progress initialized: {'completed': 0, 'total': 17, 'error': None}
2025-06-03 15:31:37,262 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'ai_processing' processing finished. Status: completed
2025-06-03 15:31:37,263 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'ai_processing' ended with success: True
2025-06-03 15:31:37,264 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'ai_processing' successful. Next step is 'scoring'.
2025-06-03 15:31:37,271 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Queuing professional step 'scoring'.
2025-06-03 15:31:37,273 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:31:37,276 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:31:37,278 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: queued_step). Task might be completed or failed.
2025-06-03 15:31:37,281 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Starting execution of professional step: 'scoring'
2025-06-03 15:31:37,285 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32, Step 'scoring': Determined input file: 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/ai_processing_report.xlsx'
2025-06-03 15:31:37,286 - WebServerAppLogger - INFO - [main_web.execute_professional_step:392] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32 'scoring': Input 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/ai_processing_report.xlsx', Output 'outputs/453f4f1e-c290-4f84-b2d7-618b2d830a32/final_scoring_report.xlsx'
2025-06-03 15:31:37,350 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Step 'scoring' processing finished. Status: completed
2025-06-03 15:31:37,351 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: Updating flow. Step 'scoring' ended with success: True
2025-06-03 15:31:37,352 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:484] - Task 453f4f1e-c290-4f84-b2d7-618b2d830a32: All professional steps completed successfully.
2025-06-03 15:31:37,355 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: completed). Task might be completed or failed.
2025-06-03 15:31:37,359 - WebServerAppLogger - WARNING - [main_web.process_professional_next_step:618] - Next step request for task 453f4f1e-c290-4f84-b2d7-618b2d830a32: No next_step_name defined (current status: completed). Task might be completed or failed.
2025-06-03 15:31:37,373 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: c700ad6c-b827-4332-aa26-c960110210cc) to 'uploads/c700ad6c-b827-4332-aa26-c960110210cc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-03 15:31:37,397 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 06:55:44,161 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 06:56:03,161 - WebServerAppLogger - INFO - [main_web.upload_excel_file:515] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: ca5da6fc-d6de-4864-8957-3e82fde874a8) to 'uploads/ca5da6fc-d6de-4864-8957-3e82fde874a8/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 06:56:03,377 - WebServerAppLogger - INFO - [main_web.upload_excel_file:522] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 06:56:09,970 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:552] - Initiating task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c with payload: {'file_id': 'ca5da6fc-d6de-4864-8957-3e82fde874a8', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 06:56:09,986 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:562] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Copied initial file from uploads/ca5da6fc-d6de-4864-8957-3e82fde874a8/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 06:56:09,990 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:591] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c fully initialized in task_statuses. Mode: professional
2025-06-04 06:56:09,991 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:596] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 06:56:12,225 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Queuing professional step 'preprocess'.
2025-06-04 06:56:12,227 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Starting execution of professional step: 'preprocess'
2025-06-04 06:56:12,228 - WebServerAppLogger - INFO - [main_web.execute_professional_step:307] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'preprocess': Using initial task file: '/usr/src/web_app/outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 06:56:12,231 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'preprocess': Determined input file: '/usr/src/web_app/outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 06:56:12,284 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'preprocess' processing finished. Status: completed
2025-06-04 06:56:12,285 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Updating flow. Step 'preprocess' ended with success: True
2025-06-04 06:56:12,285 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'preprocess' successful. Next step is 'richtext'.
2025-06-04 06:56:29,444 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Queuing professional step 'richtext'.
2025-06-04 06:56:29,445 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Starting execution of professional step: 'richtext'
2025-06-04 06:56:29,448 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'richtext': Determined input file: 'outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/preprocessed_output.json'
2025-06-04 06:56:29,483 - WebServerAppLogger - INFO - [main_web.execute_professional_step:414] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'richtext' processing finished. Status: completed
2025-06-04 06:56:29,483 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:442] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Updating flow. Step 'richtext' ended with success: True
2025-06-04 06:56:29,484 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:480] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Step 'richtext' successful. Next step is 'ai_processing'.
2025-06-04 06:57:05,195 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:666] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Queuing professional step 'ai_processing'.
2025-06-04 06:57:05,196 - WebServerAppLogger - INFO - [main_web.execute_professional_step:278] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c: Starting execution of professional step: 'ai_processing'
2025-06-04 06:57:05,199 - WebServerAppLogger - INFO - [main_web.execute_professional_step:323] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c, Step 'ai_processing': Determined input file: 'outputs/38cc45b8-25a4-407b-98a6-f92f2afe4c9c/preprocessed_output.json'
2025-06-04 06:57:05,203 - WebServerAppLogger - INFO - [main_web.execute_professional_step:355] - Task 38cc45b8-25a4-407b-98a6-f92f2afe4c9c 'ai_processing': AI progress initialized: {'completed': 0, 'total': 17, 'error': None}
2025-06-04 07:16:47,023 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 07:16:57,298 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 95a01047-5dfc-4e72-8663-c130cdeda290) to 'uploads/95a01047-5dfc-4e72-8663-c130cdeda290/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 07:16:57,496 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 07:17:02,836 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task 26e0a586-0cd2-4372-a313-a4d10d8cd22a with payload: {'file_id': '95a01047-5dfc-4e72-8663-c130cdeda290', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 07:17:02,852 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task 26e0a586-0cd2-4372-a313-a4d10d8cd22a: Copied initial file from uploads/95a01047-5dfc-4e72-8663-c130cdeda290/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/26e0a586-0cd2-4372-a313-a4d10d8cd22a/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 07:17:02,856 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task 26e0a586-0cd2-4372-a313-a4d10d8cd22a fully initialized in task_statuses. Mode: professional
2025-06-04 07:17:02,857 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task 26e0a586-0cd2-4372-a313-a4d10d8cd22a: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 07:24:53,005 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 07:25:01,389 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 188e3a7c-9e97-4691-825d-cde72f1fbc86) to 'uploads/188e3a7c-9e97-4691-825d-cde72f1fbc86/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 07:25:01,521 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 07:25:04,979 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task 68aa5df0-591c-4e96-a8d8-3ef5dd87787c with payload: {'file_id': '188e3a7c-9e97-4691-825d-cde72f1fbc86', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 07:25:04,997 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task 68aa5df0-591c-4e96-a8d8-3ef5dd87787c: Copied initial file from uploads/188e3a7c-9e97-4691-825d-cde72f1fbc86/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/68aa5df0-591c-4e96-a8d8-3ef5dd87787c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 07:25:05,001 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task 68aa5df0-591c-4e96-a8d8-3ef5dd87787c fully initialized in task_statuses. Mode: professional
2025-06-04 07:25:05,002 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task 68aa5df0-591c-4e96-a8d8-3ef5dd87787c: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 07:25:51,177 - WebServerAppLogger - WARNING - [main_web.get_task_status_endpoint:727] - Status request: Task 26e0a586-0cd2-4372-a313-a4d10d8cd22a not found.
2025-06-04 07:48:42,683 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 07:48:49,968 - WebServerAppLogger - WARNING - [main_web.get_task_status_endpoint:727] - Status request: Task 68aa5df0-591c-4e96-a8d8-3ef5dd87787c not found.
2025-06-04 07:49:00,266 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 7b774659-ce0b-471a-ba16-61d0f57516e1) to 'uploads/7b774659-ce0b-471a-ba16-61d0f57516e1/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 07:49:00,400 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 07:49:04,242 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task cd3bf010-546c-4585-b246-dfe07ca9b6cc with payload: {'file_id': '7b774659-ce0b-471a-ba16-61d0f57516e1', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 07:49:04,259 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task cd3bf010-546c-4585-b246-dfe07ca9b6cc: Copied initial file from uploads/7b774659-ce0b-471a-ba16-61d0f57516e1/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/cd3bf010-546c-4585-b246-dfe07ca9b6cc/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 07:49:04,264 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task cd3bf010-546c-4585-b246-dfe07ca9b6cc fully initialized in task_statuses. Mode: professional
2025-06-04 07:49:04,265 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task cd3bf010-546c-4585-b246-dfe07ca9b6cc: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 09:23:22,019 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 09:23:49,830 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 72fb353d-cf64-43f4-9085-d3509b716286) to 'uploads/72fb353d-cf64-43f4-9085-d3509b716286/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 09:23:49,991 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 09:23:51,090 - WebServerAppLogger - WARNING - [main_web.get_task_status_endpoint:727] - Status request: Task cd3bf010-546c-4585-b246-dfe07ca9b6cc not found.
2025-06-04 09:24:02,814 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task 3eac4589-71e4-43fa-854f-6180abc2e975 with payload: {'file_id': '72fb353d-cf64-43f4-9085-d3509b716286', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 09:24:02,835 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task 3eac4589-71e4-43fa-854f-6180abc2e975: Copied initial file from uploads/72fb353d-cf64-43f4-9085-d3509b716286/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/3eac4589-71e4-43fa-854f-6180abc2e975/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 09:24:02,842 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task 3eac4589-71e4-43fa-854f-6180abc2e975 fully initialized in task_statuses. Mode: professional
2025-06-04 09:24:02,843 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task 3eac4589-71e4-43fa-854f-6180abc2e975: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 09:28:10,786 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 09:28:27,273 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 5387cea8-aaed-4e9a-92e3-4b4343a2b2ac) to 'uploads/5387cea8-aaed-4e9a-92e3-4b4343a2b2ac/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 09:28:27,432 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 09:28:34,311 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task 31174b3f-43d3-49a5-b9f0-0b1e9e32cb8d with payload: {'file_id': '5387cea8-aaed-4e9a-92e3-4b4343a2b2ac', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 09:28:34,331 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task 31174b3f-43d3-49a5-b9f0-0b1e9e32cb8d: Copied initial file from uploads/5387cea8-aaed-4e9a-92e3-4b4343a2b2ac/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/31174b3f-43d3-49a5-b9f0-0b1e9e32cb8d/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 09:28:34,336 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task 31174b3f-43d3-49a5-b9f0-0b1e9e32cb8d fully initialized in task_statuses. Mode: professional
2025-06-04 09:28:34,337 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task 31174b3f-43d3-49a5-b9f0-0b1e9e32cb8d: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 11:05:55,904 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 11:05:58,856 - WebServerAppLogger - WARNING - [main_web.get_task_status_endpoint:727] - Status request: Task 31174b3f-43d3-49a5-b9f0-0b1e9e32cb8d not found.
2025-06-04 11:06:15,544 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: e41db03b-1fe4-45e5-acc3-74232afd35b7) to 'uploads/e41db03b-1fe4-45e5-acc3-74232afd35b7/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:06:15,700 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 11:06:20,916 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task 23263911-c6bb-4ea9-a3ef-856a4feb7d5a with payload: {'file_id': 'e41db03b-1fe4-45e5-acc3-74232afd35b7', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 11:06:20,936 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task 23263911-c6bb-4ea9-a3ef-856a4feb7d5a: Copied initial file from uploads/e41db03b-1fe4-45e5-acc3-74232afd35b7/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/23263911-c6bb-4ea9-a3ef-856a4feb7d5a/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 11:06:20,940 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task 23263911-c6bb-4ea9-a3ef-856a4feb7d5a fully initialized in task_statuses. Mode: professional
2025-06-04 11:06:20,941 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task 23263911-c6bb-4ea9-a3ef-856a4feb7d5a: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 11:09:44,106 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 11:09:45,003 - WebServerAppLogger - WARNING - [main_web.get_task_status_endpoint:727] - Status request: Task 23263911-c6bb-4ea9-a3ef-856a4feb7d5a not found.
2025-06-04 11:09:55,061 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 63380634-faba-403d-91b8-b9c1c3e090f3) to 'uploads/63380634-faba-403d-91b8-b9c1c3e090f3/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:09:55,273 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 11:09:57,950 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task d7f167ee-a664-44f6-9067-bb5e1442d35c with payload: {'file_id': '63380634-faba-403d-91b8-b9c1c3e090f3', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 11:09:57,968 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: Copied initial file from uploads/63380634-faba-403d-91b8-b9c1c3e090f3/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/d7f167ee-a664-44f6-9067-bb5e1442d35c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 11:09:57,973 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c fully initialized in task_statuses. Mode: professional
2025-06-04 11:09:57,974 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 11:09:58,989 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:623] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: User manually specified step to execute: preprocess
2025-06-04 11:09:58,990 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:643] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c, Preparing to run step 'preprocess': Received payload: {'step_name': 'preprocess'}
2025-06-04 11:09:58,991 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:677] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: Queuing professional step 'preprocess'.
2025-06-04 11:09:58,993 - WebServerAppLogger - INFO - [main_web.execute_professional_step:279] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: Starting execution of professional step: 'preprocess'
2025-06-04 11:09:58,994 - WebServerAppLogger - INFO - [main_web.execute_professional_step:308] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c, Step 'preprocess': Using initial task file: '/usr/src/web_app/outputs/d7f167ee-a664-44f6-9067-bb5e1442d35c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:09:58,997 - WebServerAppLogger - INFO - [main_web.execute_professional_step:324] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c, Step 'preprocess': Determined input file: '/usr/src/web_app/outputs/d7f167ee-a664-44f6-9067-bb5e1442d35c/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:09:59,041 - WebServerAppLogger - INFO - [main_web.execute_professional_step:415] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: Step 'preprocess' processing finished. Status: completed
2025-06-04 11:09:59,041 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:443] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: Updating flow. Step 'preprocess' ended with success: True
2025-06-04 11:09:59,042 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:482] - Task d7f167ee-a664-44f6-9067-bb5e1442d35c: Step 'preprocess' successful. User can manually proceed to 'richtext'.
2025-06-04 11:19:12,899 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 11:19:13,901 - WebServerAppLogger - WARNING - [main_web.get_task_status_endpoint:727] - Status request: Task d7f167ee-a664-44f6-9067-bb5e1442d35c not found.
2025-06-04 11:19:23,501 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: fed1a9f1-1d0e-4efb-babe-7da4ad00cddd) to 'uploads/fed1a9f1-1d0e-4efb-babe-7da4ad00cddd/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:19:23,720 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 11:19:26,713 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825 with payload: {'file_id': 'fed1a9f1-1d0e-4efb-babe-7da4ad00cddd', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 11:19:26,729 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Copied initial file from uploads/fed1a9f1-1d0e-4efb-babe-7da4ad00cddd/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/38bf94e4-7d1e-4c6e-a4b0-67ee236c8825/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 11:19:26,734 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825 fully initialized in task_statuses. Mode: professional
2025-06-04 11:19:26,735 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 11:19:27,748 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:623] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: User manually specified step to execute: preprocess
2025-06-04 11:19:27,749 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:643] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825, Preparing to run step 'preprocess': Received payload: {'step_name': 'preprocess'}
2025-06-04 11:19:27,750 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:677] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Queuing professional step 'preprocess'.
2025-06-04 11:19:27,751 - WebServerAppLogger - INFO - [main_web.execute_professional_step:279] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Starting execution of professional step: 'preprocess'
2025-06-04 11:19:27,753 - WebServerAppLogger - INFO - [main_web.execute_professional_step:308] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825, Step 'preprocess': Using initial task file: '/usr/src/web_app/outputs/38bf94e4-7d1e-4c6e-a4b0-67ee236c8825/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:19:27,756 - WebServerAppLogger - INFO - [main_web.execute_professional_step:324] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825, Step 'preprocess': Determined input file: '/usr/src/web_app/outputs/38bf94e4-7d1e-4c6e-a4b0-67ee236c8825/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:19:27,787 - WebServerAppLogger - INFO - [main_web.execute_professional_step:415] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Step 'preprocess' processing finished. Status: completed
2025-06-04 11:19:27,788 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:443] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Updating flow. Step 'preprocess' ended with success: True
2025-06-04 11:19:27,789 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:482] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Step 'preprocess' successful. User can manually proceed to 'richtext'.
2025-06-04 11:19:36,178 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:623] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: User manually specified step to execute: richtext
2025-06-04 11:19:36,179 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:643] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825, Preparing to run step 'richtext': Received payload: {'step_name': 'richtext'}
2025-06-04 11:19:36,180 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:677] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Queuing professional step 'richtext'.
2025-06-04 11:19:36,181 - WebServerAppLogger - INFO - [main_web.execute_professional_step:279] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Starting execution of professional step: 'richtext'
2025-06-04 11:19:36,183 - WebServerAppLogger - INFO - [main_web.execute_professional_step:324] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825, Step 'richtext': Determined input file: 'outputs/38bf94e4-7d1e-4c6e-a4b0-67ee236c8825/preprocessed_output.json'
2025-06-04 11:19:36,213 - WebServerAppLogger - INFO - [main_web.execute_professional_step:415] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Step 'richtext' processing finished. Status: completed
2025-06-04 11:19:36,214 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:443] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Updating flow. Step 'richtext' ended with success: True
2025-06-04 11:19:36,214 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:482] - Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825: Step 'richtext' successful. User can manually proceed to 'ai_processing'.
2025-06-04 11:23:44,312 - WebServerAppLogger - INFO - [main_web.<module>:68] - WebServerAppLogger initialized and configured to log to file and potentially console.
2025-06-04 11:23:44,713 - WebServerAppLogger - WARNING - [main_web.get_task_status_endpoint:727] - Status request: Task 38bf94e4-7d1e-4c6e-a4b0-67ee236c8825 not found.
2025-06-04 11:23:52,863 - WebServerAppLogger - INFO - [main_web.upload_excel_file:518] - File uploaded: 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx' (upload_id: 2a62ada8-ca8d-4b91-a16a-d74b28e0d69a) to 'uploads/2a62ada8-ca8d-4b91-a16a-d74b28e0d69a/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:23:52,987 - WebServerAppLogger - INFO - [main_web.upload_excel_file:525] - Extracted headers from 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx': ['Record ID', '文件名 File Name', 'Chapters 章节', '路径 Path', '段落 Scenario', '评论 Comments', '说话者Character Who Speaks', '角色介绍 Character Intro', '角色语言风格 Speech Style', '参考资料 References', '注意 Note', '长度Length and Conciseness', 'Chinese (Simplified)', 'English (United Kingdom)', '语气 Tone', 'Translation']
2025-06-04 11:23:57,657 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:555] - Initiating task 207e594c-6bfa-4e57-831b-a4e7b6e7676f with payload: {'file_id': '2a62ada8-ca8d-4b91-a16a-d74b28e0d69a', 'filename': 'B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx', 'mode': 'professional', 'params': {'enable_literal_translation': False, 'enable_decomposition': False, 'skip_scoring': False, 'column_mappings': {'record_id': 'Record ID', 'file_name': '文件名 File Name', 'source_text': 'English (United Kingdom)', 'translation_text': 'Translation', 'comments': '评论 Comments', 'path': '路径 Path', 'scenario': '段落 Scenario', 'char_intro': '说话者Character Who Speaks', 'char_style': '角色语言风格 Speech Style', 'tone': '语气 Tone', 'note': '注意 Note'}}}
2025-06-04 11:23:57,673 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:565] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Copied initial file from uploads/2a62ada8-ca8d-4b91-a16a-d74b28e0d69a/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx to outputs/207e594c-6bfa-4e57-831b-a4e7b6e7676f/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx
2025-06-04 11:23:57,678 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:594] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f fully initialized in task_statuses. Mode: professional
2025-06-04 11:23:57,678 - WebServerAppLogger - INFO - [main_web.initiate_processing_task:599] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Professional mode initiated. Ready for user to trigger 'preprocess' step.
2025-06-04 11:23:58,694 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:623] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: User manually specified step to execute: preprocess
2025-06-04 11:23:58,694 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:643] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f, Preparing to run step 'preprocess': Received payload: {'step_name': 'preprocess'}
2025-06-04 11:23:58,695 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:677] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Queuing professional step 'preprocess'.
2025-06-04 11:23:58,697 - WebServerAppLogger - INFO - [main_web.execute_professional_step:279] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Starting execution of professional step: 'preprocess'
2025-06-04 11:23:58,698 - WebServerAppLogger - INFO - [main_web.execute_professional_step:308] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f, Step 'preprocess': Using initial task file: '/usr/src/web_app/outputs/207e594c-6bfa-4e57-831b-a4e7b6e7676f/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:23:58,701 - WebServerAppLogger - INFO - [main_web.execute_professional_step:324] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f, Step 'preprocess': Determined input file: '/usr/src/web_app/outputs/207e594c-6bfa-4e57-831b-a4e7b6e7676f/B1_EN-CS Final Branch_AG View - Yellow Wind Ridge 黄风岭-cze(1).xlsx'
2025-06-04 11:23:58,728 - WebServerAppLogger - INFO - [main_web.execute_professional_step:415] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Step 'preprocess' processing finished. Status: completed
2025-06-04 11:23:58,729 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:443] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Updating flow. Step 'preprocess' ended with success: True
2025-06-04 11:23:58,730 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:482] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Step 'preprocess' successful. User can manually proceed to 'richtext'.
2025-06-04 11:24:02,579 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:623] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: User manually specified step to execute: richtext
2025-06-04 11:24:02,580 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:643] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f, Preparing to run step 'richtext': Received payload: {'step_name': 'richtext'}
2025-06-04 11:24:02,582 - WebServerAppLogger - INFO - [main_web.process_professional_next_step:677] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Queuing professional step 'richtext'.
2025-06-04 11:24:02,583 - WebServerAppLogger - INFO - [main_web.execute_professional_step:279] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Starting execution of professional step: 'richtext'
2025-06-04 11:24:02,586 - WebServerAppLogger - INFO - [main_web.execute_professional_step:324] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f, Step 'richtext': Determined input file: 'outputs/207e594c-6bfa-4e57-831b-a4e7b6e7676f/preprocessed_output.json'
2025-06-04 11:24:02,612 - WebServerAppLogger - INFO - [main_web.execute_professional_step:415] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Step 'richtext' processing finished. Status: completed
2025-06-04 11:24:02,613 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:443] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Updating flow. Step 'richtext' ended with success: True
2025-06-04 11:24:02,614 - WebServerAppLogger - INFO - [main_web._update_professional_task_flow:482] - Task 207e594c-6bfa-4e57-831b-a4e7b6e7676f: Step 'richtext' successful. User can manually proceed to 'ai_processing'.
