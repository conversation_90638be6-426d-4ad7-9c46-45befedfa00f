import streamlit as st
import pandas as pd
import io

def make_download_button(data, file_name, label="下载产出文件"):
    if data is not None:
        if isinstance(data, pd.DataFrame):
            if not data.empty:
                output = io.BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    data.to_excel(writer, index=False)
                processed_data = output.getvalue()
                mime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            else:
                st.info(f"沒有可下載的數據 ({label}).")
                return
        elif isinstance(data, str): # JSON string
            processed_data = data.encode('utf-8')
            mime = "application/json"
        elif isinstance(data, bytes): # Already bytes (e.g. from BytesIO)
             processed_data = data
             mime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" if file_name.endswith(".xlsx") else "application/octet-stream"
        else:
            st.error("不支持的下載數據類型")
            return

        st.download_button(
            label=label,
            data=processed_data,
            file_name=file_name,
            mime=mime
        )
    else:
        st.info(f"沒有可下載的數據 ({label}).")

def complete_step_and_proceed(current_step_name, WORKFLOW_STEPS):
    old_index = st.session_state.current_step_index
    
    st.session_state.step_status[current_step_name] = "completed"
    if st.session_state.current_step_index < len(WORKFLOW_STEPS) - 1:
        st.session_state.current_step_index += 1
        next_step_name = WORKFLOW_STEPS[st.session_state.current_step_index]
        st.session_state.step_status[next_step_name] = "active"
        st.success(f"步驟切換: {old_index} -> {st.session_state.current_step_index}, 當前步驟: {next_step_name}")
    else:
        st.session_state.current_step_index = len(WORKFLOW_STEPS) # Mark as beyond last step
        st.success(f"工作流完成: {old_index} -> {st.session_state.current_step_index}")
    
    st.rerun()

def fail_step(current_step_name, message="處理失敗"):
    st.session_state.step_status[current_step_name] = "failed"
    st.error(message)
    st.rerun()
